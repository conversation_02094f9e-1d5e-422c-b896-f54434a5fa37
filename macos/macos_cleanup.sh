#!/bin/bash

# macOS System Cleanup Script
# 适用于 macOS Ventura 13.6.4
# 安全清理系统临时文件、缓存文件等

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取文件夹大小
get_folder_size() {
    if [ -d "$1" ]; then
        du -sh "$1" 2>/dev/null | cut -f1 || echo "0B"
    else
        echo "0B"
    fi
}

# 安全删除函数
safe_remove() {
    local path="$1"
    local description="$2"
    
    if [ -d "$path" ] || [ -f "$path" ]; then
        local size=$(get_folder_size "$path")
        log_info "清理 $description ($size)"
        rm -rf "$path" 2>/dev/null || log_warning "无法删除 $path"
        log_success "已清理 $description"
    fi
}

# 清空文件夹内容但保留文件夹
empty_folder() {
    local path="$1"
    local description="$2"
    
    if [ -d "$path" ]; then
        local size=$(get_folder_size "$path")
        log_info "清空 $description ($size)"
        rm -rf "$path"/* 2>/dev/null || true
        rm -rf "$path"/.[^.]* 2>/dev/null || true
        log_success "已清空 $description"
    fi
}

echo "=================================================="
echo "          macOS 系统清理脚本"
echo "          适用于 macOS Ventura 13.6.4"
echo "=================================================="
echo

# 检查是否为管理员权限
if [[ $EUID -eq 0 ]]; then
    log_warning "检测到管理员权限，将进行系统级清理"
    ADMIN_MODE=true
else
    log_info "用户模式，将清理用户级缓存和临时文件"
    ADMIN_MODE=false
fi

echo
log_info "开始清理系统缓存和临时文件..."
echo

# 1. 清理用户缓存
log_info "=== 清理用户缓存 ==="
empty_folder "$HOME/Library/Caches" "用户应用缓存"

# 清理特定应用缓存
declare -a cache_dirs=(
    "$HOME/Library/Caches/com.apple.Safari"
    "$HOME/Library/Caches/Google/Chrome"
    "$HOME/Library/Caches/Firefox"
    "$HOME/Library/Caches/com.microsoft.VSCode"
    "$HOME/Library/Caches/com.apple.dt.Xcode"
    "$HOME/Library/Caches/pip"
    "$HOME/Library/Caches/npm"
    "$HOME/Library/Caches/yarn"
)

for cache_dir in "${cache_dirs[@]}"; do
    if [ -d "$cache_dir" ]; then
        empty_folder "$cache_dir" "$(basename "$cache_dir") 缓存"
    fi
done

# 2. 清理临时文件
log_info "=== 清理临时文件 ==="
empty_folder "/tmp" "系统临时文件"
empty_folder "$HOME/.Trash" "用户回收站"

# 3. 清理日志文件
log_info "=== 清理日志文件 ==="
empty_folder "$HOME/Library/Logs" "用户日志文件"

# 4. 清理下载文件夹中的临时文件
log_info "=== 清理下载临时文件 ==="
find "$HOME/Downloads" -name "*.tmp" -delete 2>/dev/null || true
find "$HOME/Downloads" -name "*.temp" -delete 2>/dev/null || true
find "$HOME/Downloads" -name ".DS_Store" -delete 2>/dev/null || true

# 5. 清理系统缓存 (需要管理员权限)
if [ "$ADMIN_MODE" = true ]; then
    log_info "=== 清理系统级缓存 ==="
    empty_folder "/Library/Caches" "系统应用缓存"
    empty_folder "/System/Library/Caches" "系统库缓存"
    empty_folder "/var/log" "系统日志"
    empty_folder "/private/var/log" "私有系统日志"
fi

# 6. 清理 Spotlight 索引缓存
log_info "=== 清理 Spotlight 缓存 ==="
safe_remove "$HOME/Library/Metadata/CoreSpotlight" "Spotlight 索引缓存"

# 7. 清理字体缓存
log_info "=== 清理字体缓存 ==="
safe_remove "$HOME/Library/Caches/com.apple.ATS" "字体缓存"

# 8. 清理 DNS 缓存
log_info "=== 清理 DNS 缓存 ==="
sudo dscacheutil -flushcache 2>/dev/null && log_success "DNS 缓存已清理" || log_warning "无法清理 DNS 缓存"

# 9. 清理 .DS_Store 文件
log_info "=== 清理 .DS_Store 文件 ==="
find "$HOME" -name ".DS_Store" -delete 2>/dev/null && log_success "已清理 .DS_Store 文件" || true

# 10. 清理浏览器缓存
log_info "=== 清理浏览器特定缓存 ==="
# Safari
safe_remove "$HOME/Library/Safari/LocalStorage" "Safari 本地存储"
safe_remove "$HOME/Library/Safari/Databases" "Safari 数据库"

# Chrome
safe_remove "$HOME/Library/Application Support/Google/Chrome/Default/Application Cache" "Chrome 应用缓存"

# 11. 清理开发工具缓存
log_info "=== 清理开发工具缓存 ==="
safe_remove "$HOME/.npm/_cacache" "npm 缓存"
safe_remove "$HOME/.yarn/cache" "Yarn 缓存"
safe_remove "$HOME/.gradle/caches" "Gradle 缓存"
safe_remove "$HOME/.m2/repository" "Maven 缓存"

echo
log_info "=== 清理完成统计 ==="

# 显示磁盘使用情况
echo "当前磁盘使用情况:"
df -h / | tail -1

echo
log_success "系统清理完成！"
log_info "建议重启系统以确保所有缓存完全清理"

echo
echo "=================================================="
echo "清理脚本执行完毕"
echo "如需更深度清理，请以管理员权限运行: sudo $0"
echo "=================================================="
