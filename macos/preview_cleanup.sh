#!/bin/bash

# macOS 系统清理预览脚本
# 显示将要清理的文件和大小，但不实际删除

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_found() {
    echo -e "${GREEN}[FOUND]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 获取文件夹大小
get_folder_size() {
    if [ -d "$1" ]; then
        du -sh "$1" 2>/dev/null | cut -f1 || echo "0B"
    else
        echo "不存在"
    fi
}

# 检查并显示文件夹信息
check_folder() {
    local path="$1"
    local description="$2"
    
    if [ -d "$path" ]; then
        local size=$(get_folder_size "$path")
        local count=$(find "$path" -type f 2>/dev/null | wc -l | tr -d ' ')
        log_found "$description: $size ($count 个文件)"
    else
        echo "   $description: 不存在"
    fi
}

echo "=================================================="
echo "          macOS 系统清理预览"
echo "          显示可清理的文件和大小"
echo "=================================================="
echo

total_size=0

log_info "=== 用户缓存文件 ==="
check_folder "$HOME/Library/Caches" "用户应用缓存"
check_folder "$HOME/Library/Caches/com.apple.Safari" "Safari 缓存"
check_folder "$HOME/Library/Caches/Google/Chrome" "Chrome 缓存"
check_folder "$HOME/Library/Caches/Firefox" "Firefox 缓存"
check_folder "$HOME/Library/Caches/com.microsoft.VSCode" "VSCode 缓存"
check_folder "$HOME/Library/Caches/com.apple.dt.Xcode" "Xcode 缓存"
check_folder "$HOME/Library/Caches/pip" "Python pip 缓存"
check_folder "$HOME/Library/Caches/npm" "npm 缓存"
check_folder "$HOME/Library/Caches/yarn" "Yarn 缓存"

echo
log_info "=== 临时文件 ==="
check_folder "/tmp" "系统临时文件"
check_folder "$HOME/.Trash" "回收站"

echo
log_info "=== 日志文件 ==="
check_folder "$HOME/Library/Logs" "用户日志文件"

echo
log_info "=== 下载临时文件 ==="
if [ -d "$HOME/Downloads" ]; then
    tmp_count=$(find "$HOME/Downloads" -name "*.tmp" -o -name "*.temp" 2>/dev/null | wc -l | tr -d ' ')
    ds_count=$(find "$HOME/Downloads" -name ".DS_Store" 2>/dev/null | wc -l | tr -d ' ')
    log_found "下载临时文件: $tmp_count 个 .tmp/.temp 文件"
    log_found "DS_Store 文件: $ds_count 个文件"
fi

echo
log_info "=== 系统级缓存 (需要 sudo 权限) ==="
check_folder "/Library/Caches" "系统应用缓存"
check_folder "/System/Library/Caches" "系统库缓存"
check_folder "/var/log" "系统日志"
check_folder "/private/var/log" "私有系统日志"

echo
log_info "=== 特殊缓存 ==="
check_folder "$HOME/Library/Metadata/CoreSpotlight" "Spotlight 索引缓存"
check_folder "$HOME/Library/Caches/com.apple.ATS" "字体缓存"
check_folder "$HOME/Library/Safari/LocalStorage" "Safari 本地存储"
check_folder "$HOME/Library/Safari/Databases" "Safari 数据库"
check_folder "$HOME/Library/Application Support/Google/Chrome/Default/Application Cache" "Chrome 应用缓存"

echo
log_info "=== 开发工具缓存 ==="
check_folder "$HOME/.npm/_cacache" "npm 缓存"
check_folder "$HOME/.yarn/cache" "Yarn 缓存"
check_folder "$HOME/.gradle/caches" "Gradle 缓存"
check_folder "$HOME/.m2/repository" "Maven 仓库"

echo
log_info "=== 全局 .DS_Store 文件 ==="
ds_global_count=$(find "$HOME" -name ".DS_Store" 2>/dev/null | wc -l | tr -d ' ')
log_found "全局 .DS_Store 文件: $ds_global_count 个文件"

echo
echo "=================================================="
log_info "当前磁盘使用情况:"
df -h / | head -1
df -h / | tail -1

echo
echo "=================================================="
echo "预览完成！"
echo "要执行实际清理，请运行: ./macos_cleanup.sh"
echo "要进行深度清理，请运行: sudo ./macos_cleanup.sh"
echo "=================================================="
