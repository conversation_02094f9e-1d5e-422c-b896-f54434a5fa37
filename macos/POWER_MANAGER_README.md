# macOS 电源管理工具

智能的macOS电源管理脚本，支持多种使用场景的电源模式切换。

## 🚀 快速开始

### 安装配置
```bash
# 1. 配置sudo免密（一次性设置）
./setup_power_sudoers.sh

# 2. 复制到系统路径（可选）
cp power_manager.sh ~/bin/power_manager
chmod +x ~/bin/power_manager

# 3. 测试运行
./power_manager.sh status
```

## 📋 使用方法

### 基本命令
```bash
# 查看当前电源状态
power_manager status

# 切换到手动控制模式（推荐日常使用）
power_manager manual

# 切换到远程访问模式（支持网络唤醒）
power_manager remote

# 切换到办公室模式（自动节能）
power_manager office

# 强制立即睡眠
power_manager sleep

# 测试网络唤醒配置
power_manager wake-test
```

### 快捷命令
```bash
power_manager m    # manual模式
power_manager r    # remote模式  
power_manager o    # office模式
power_manager s    # 立即睡眠
power_manager st   # 查看状态
power_manager wt   # 网络唤醒测试
```

## 🔧 电源模式详解

### 1. 手动控制模式 (`manual`)
**适用场景**: 日常在家办公，需要完全控制睡眠时机

**特点**:
- ✅ 永不自动睡眠
- ✅ 显示器15分钟后关闭
- ❌ 禁用网络唤醒
- ❌ 禁用Power Nap
- 🎯 使用 `Cmd+Option+电源键` 手动睡眠

### 2. 远程访问模式 (`remote`)
**适用场景**: 需要远程访问Mac（Tailscale/SSH）

**特点**:
- ✅ 永不自动睡眠（仍需手动控制）
- ✅ 启用网络唤醒（支持远程唤醒）
- ✅ 启用Power Nap（保持同步）
- ✅ 支持iPhone/iPad接近唤醒
- 🎯 支持 `tailscale wake` 远程唤醒

### 3. 办公室模式 (`office`)
**适用场景**: 办公室使用，需要自动节能但快速响应

**特点**:
- ⚡ 电源模式: 显示器8分钟，系统10分钟后睡眠
- 🔋 电池模式: 显示器3分钟，系统5分钟后睡眠
- ✅ 支持快速网络唤醒
- ✅ 优化响应速度

## 🛠️ 实用功能

### 网络唤醒测试
```bash
power_manager wake-test
```
显示当前网络信息和唤醒命令：
- 本机IP和MAC地址
- wakeonlan命令示例
- Tailscale唤醒命令
- 当前网络唤醒状态

### 状态查看
```bash
power_manager status
```
显示详细信息：
- 当前电源设置
- 睡眠阻止状态
- 电池状态
- 网络信息

## 📱 使用场景

### 日常在家办公
```bash
# 设置手动控制，完全掌控睡眠时机
power_manager manual

# 需要睡眠时
power_manager sleep
# 或使用快捷键: Cmd+Option+电源键
```

### 远程工作场景
```bash
# 启用远程访问模式
power_manager remote

# 在其他设备上唤醒Mac
tailscale wake YOUR_MAC_IP
# 或
wakeonlan YOUR_MAC_ADDRESS
```

### 办公室使用
```bash
# 自动节能模式
power_manager office

# 离开时会自动睡眠，回来时快速唤醒
```

## ⚡ 快捷键设置

### 系统快捷键
- **手动睡眠**: `Cmd + Option + 电源键`
- **强制睡眠**: `power_manager sleep`

### 自定义快捷键（可选）
可以在系统偏好设置中为脚本设置快捷键：
- `Cmd+Shift+S` → `power_manager sleep`
- `Cmd+Shift+M` → `power_manager manual`
- `Cmd+Shift+R` → `power_manager remote`

## 🔍 故障排除

### 权限问题
```bash
# 如果提示需要密码，重新配置sudo免密
./setup_power_sudoers.sh

# 测试sudo权限
sudo pmset -g custom
```

### 网络唤醒不工作
```bash
# 检查网络唤醒状态
power_manager wake-test

# 确保路由器支持WOL
# 确保Mac在同一网络或通过VPN连接
```

### 显示器睡眠警告
如果看到 "Display sleep should have a lower timeout" 警告：
```bash
# 重新设置模式即可修复
power_manager manual
power_manager office
```

## 📝 日志查看

所有操作都会记录到日志文件：
```bash
# 查看日志
tail -f ~/power_manager.log

# 查看最近操作
tail -20 ~/power_manager.log
```

## 🎯 推荐配置

### 个人用户
- **日常**: `manual` 模式 + 手动睡眠控制
- **远程**: `remote` 模式 + Tailscale

### 办公用户  
- **办公室**: `office` 模式（自动节能）
- **在家**: `manual` 模式（手动控制）

### 开发者
- **编码时**: `manual` 模式（避免意外睡眠）
- **会议时**: `office` 模式（自动管理）