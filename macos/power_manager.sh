#!/bin/bash
# macOS 电源管理脚本
# 支持多种电源模式切换和远程访问场景
#
# 使用方法: power_manager.sh {manual|remote|office|status|sleep|wake-test}
#
# 作者: zhaoj296

set -uo pipefail

# 配置变量
LOG_FILE="$HOME/power_manager.log"

# 日志函数
log() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$msg"
    echo "$msg" >> "$LOG_FILE"
}

# 检查管理员权限
check_sudo() {
    # 直接测试pmset命令是否可以无密码执行
    if sudo -n pmset -g custom >/dev/null 2>&1; then
        return 0
    else
        log "需要管理员权限来修改电源设置"
        log "请确认sudo免密配置: sudo visudo -f /etc/sudoers.d/power_manager"
        return 1
    fi
}

# 手动控制模式 (您当前的配置)
set_manual_mode() {
    log "设置手动控制模式..."
    
    # 禁用所有自动睡眠和唤醒
    sudo pmset -a sleep 0 displaysleep 15 womp 0 powernap 0 standby 0
    sudo pmset -a autopoweroff 0 proximitywake 0
    
    log "手动控制模式已启用 ✅"
    log "- 永不自动睡眠"
    log "- 显示器15分钟后关闭"
    log "- 禁用网络唤醒"
    log "- 禁用Power Nap"
    log "- 使用 Cmd+Option+电源键 手动睡眠"
}

# 远程访问模式 (支持Tailscale等远程唤醒)
set_remote_mode() {
    log "设置远程访问模式..."
    
    if ! check_sudo; then
        exit 1
    fi
    
    # 启用网络唤醒，但保持手动睡眠控制
    sudo pmset -a sleep 0           # 仍然不自动睡眠
    sudo pmset -a womp 1            # 启用网络唤醒
    sudo pmset -a powernap 1        # 启用Power Nap用于同步
    sudo pmset -a standby 0         # 禁用standby避免深度睡眠
    sudo pmset -a proximitywake 1   # 启用接近唤醒
    
    log "远程访问模式已启用 ✅"
    log "- 支持网络唤醒 (Tailscale/SSH)"
    log "- 启用Power Nap同步"
    log "- 仍需手动睡眠控制"
    log "- 支持iPhone/iPad接近唤醒"
}

# 办公室模式 (节能但响应快)
set_office_mode() {
    log "设置办公室模式..."
    
    if ! check_sudo; then
        exit 1
    fi
    
    # 分别设置电源和电池模式，确保显示器睡眠时间正确
    sudo pmset -c sleep 10 displaysleep 8 womp 1 powernap 0 standby 0 proximitywake 1
    sudo pmset -b sleep 5 displaysleep 3 womp 1 powernap 0 standby 0 proximitywake 1
    
    log "办公室模式已启用 ✅"
    log "- 电源模式: 显示器8分钟，系统10分钟后睡眠"
    log "- 电池模式: 显示器3分钟，系统5分钟后睡眠"
    log "- 支持快速网络唤醒"
    log "- 优化响应速度"
}

# 强制立即睡眠
force_sleep() {
    log "强制系统立即睡眠..."
    sudo pmset sleepnow
}

# 测试网络唤醒功能
test_wake_on_lan() {
    log "测试网络唤醒配置..."
    
    # 获取当前IP和MAC地址
    local ip=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | head -1 | awk '{print $2}')
    local mac=$(ifconfig | grep ether | head -1 | awk '{print $2}')
    
    echo "当前网络信息:"
    echo "IP地址: $ip"
    echo "MAC地址: $mac"
    echo ""
    echo "网络唤醒测试命令 (在其他设备上执行):"
    echo "wakeonlan $mac"
    echo ""
    echo "或使用Tailscale唤醒:"
    echo "tailscale wake $ip"
    
    # 检查当前womp设置
    local womp_status=$(pmset -g custom | grep womp | head -1 | awk '{print $2}')
    if [ "$womp_status" = "1" ]; then
        log "网络唤醒已启用 ✅"
    else
        log "网络唤醒已禁用 ❌"
        log "使用 '$0 remote' 启用远程访问模式"
    fi
}

# 显示当前状态
show_status() {
    echo "=== macOS 电源管理状态 ==="
    echo ""
    
    # 显示当前设置
    echo "当前电源设置:"
    pmset -g custom | grep -E "(sleep|womp|powernap|standby)" | head -8
    echo ""
    
    # 显示睡眠阻止情况
    echo "睡眠阻止状态:"
    pmset -g assertions | head -10
    echo ""
    
    # 显示电池状态
    echo "电池状态:"
    pmset -g batt
    echo ""
    
    # 显示网络信息
    local ip=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | head -1 | awk '{print $2}')
    local mac=$(ifconfig | grep ether | head -1 | awk '{print $2}')
    echo "网络信息:"
    echo "IP: $ip"
    echo "MAC: $mac"
}

# 主函数
main() {
    case "${1:-}" in
        manual|m)
            set_manual_mode
            ;;
        remote|r)
            set_remote_mode
            ;;
        office|o)
            set_office_mode
            ;;
        sleep|s)
            force_sleep
            ;;
        wake-test|wt)
            test_wake_on_lan
            ;;
        status|st)
            show_status
            ;;
        *)
            echo "macOS 电源管理工具"
            echo ""
            echo "用法: $0 {manual|remote|office|status|sleep|wake-test}"
            echo ""
            echo "模式说明:"
            echo "  manual   (m)  - 手动控制模式 (当前推荐)"
            echo "  remote   (r)  - 远程访问模式 (支持Tailscale唤醒)"
            echo "  office   (o)  - 办公室模式 (自动睡眠+快速唤醒)"
            echo ""
            echo "操作命令:"
            echo "  sleep    (s)  - 强制立即睡眠"
            echo "  status   (st) - 显示当前状态"
            echo "  wake-test(wt) - 测试网络唤醒配置"
            echo ""
            echo "使用场景:"
            echo "  在家办公    - manual模式 + 手动睡眠控制"
            echo "  远程工作    - remote模式 + Tailscale唤醒"
            echo "  办公室使用  - office模式 + 自动节能"
            echo ""
            echo "快捷键:"
            echo "  手动睡眠: Cmd + Option + 电源键"
            echo "  强制睡眠: $0 sleep"
            exit 1
            ;;
    esac
}

main "$@"
