# 高级配置说明

## 🔧 自定义端口配置

### 修改Mac代理服务器端口
```python
# 编辑 mac-proxy-server.py
PORT = 9999  # 修改为您想要的端口

# 相应地修改服务器配置
export http_proxy=http://*************:9999
export https_proxy=http://*************:9999
```

### 修改虚机代理服务器端口
```python
# 编辑 server-vm-proxy.py
PORT = 8080  # 修改为您想要的端口

# 相应地修改虚机配置
export http_proxy=http://*************:8080
export https_proxy=http://*************:8080
```

## 🌐 多网络环境配置

### 支持多个VPN网段
```bash
# 在setup-server.sh中添加网段检测
detect_network_segment() {
    local ip=$(echo $SSH_CLIENT | cut -d' ' -f1)
    case $ip in
        192.168.249.*) echo "VPN-A" ;;
        192.168.248.*) echo "VPN-B" ;;
        10.0.*) echo "VPN-C" ;;
        *) echo "Unknown" ;;
    esac
}
```

### 动态端口分配
```python
# 在代理服务器中添加端口检测
import socket

def find_free_port(start_port=8888):
    for port in range(start_port, start_port + 100):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('', port))
            sock.close()
            return port
        except OSError:
            continue
    raise Exception("No free port found")
```

## 🔐 安全增强配置

### 添加认证机制
```python
# 在代理服务器中添加基本认证
import base64

class AuthProxyHandler(ProxyHandler):
    def check_auth(self):
        auth_header = self.headers.get('Proxy-Authorization')
        if not auth_header:
            return False
        
        try:
            auth_type, credentials = auth_header.split(' ', 1)
            if auth_type.lower() != 'basic':
                return False
            
            username, password = base64.b64decode(credentials).decode().split(':', 1)
            return username == 'admin' and password == 'password'
        except:
            return False
    
    def proxy_request(self):
        if not self.check_auth():
            self.send_response(407, 'Proxy Authentication Required')
            self.send_header('Proxy-Authenticate', 'Basic realm="Proxy"')
            self.end_headers()
            return
        
        super().proxy_request()
```

### IP白名单限制
```python
# 添加IP访问控制
ALLOWED_IPS = ['*************/24', '*************/24']

def is_ip_allowed(client_ip):
    import ipaddress
    client = ipaddress.ip_address(client_ip)
    for allowed in ALLOWED_IPS:
        if client in ipaddress.ip_network(allowed):
            return True
    return False
```

## 📊 监控和日志

### 详细日志记录
```python
import logging
import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('proxy.log'),
        logging.StreamHandler()
    ]
)

class LoggingProxyHandler(ProxyHandler):
    def log_request(self, code='-', size='-'):
        client_ip = self.client_address[0]
        timestamp = datetime.datetime.now().isoformat()
        logging.info(f'{client_ip} - {timestamp} - {self.command} {self.path} - {code}')
```

### 流量统计
```python
class StatsProxyHandler(ProxyHandler):
    stats = {'requests': 0, 'bytes_sent': 0, 'bytes_received': 0}
    
    def proxy_request(self):
        self.stats['requests'] += 1
        # 记录流量统计
        super().proxy_request()
```

## 🚀 性能优化

### 连接池配置
```python
import urllib3

# 配置连接池
http = urllib3.PoolManager(
    num_pools=10,
    maxsize=20,
    retries=urllib3.Retry(total=3)
)
```

### 缓存配置
```python
import time
from functools import lru_cache

# DNS缓存
@lru_cache(maxsize=1000)
def resolve_hostname(hostname):
    import socket
    return socket.gethostbyname(hostname)

# 响应缓存（适用于静态资源）
class CachingProxyHandler(ProxyHandler):
    cache = {}
    cache_ttl = 300  # 5分钟
    
    def get_cache_key(self, url):
        return url
    
    def is_cacheable(self, url):
        # 只缓存特定类型的请求
        return url.endswith(('.js', '.css', '.png', '.jpg', '.gif'))
```

## 🔄 自动化部署

### 系统服务配置
```bash
# 创建systemd服务文件
cat > /etc/systemd/system/vm-proxy.service << EOF
[Unit]
Description=VM Proxy Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/server-proxy-tools
ExecStart=/usr/bin/python3 server-vm-proxy.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
systemctl enable vm-proxy.service
systemctl start vm-proxy.service
```

### Docker容器化
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY server-vm-proxy.py .

EXPOSE 3128

CMD ["python3", "server-vm-proxy.py"]
```

```bash
# 构建和运行
docker build -t vm-proxy .
docker run -d -p 3128:3128 --name vm-proxy vm-proxy
```

## 🔧 故障自动恢复

### 健康检查
```python
import threading
import time

class HealthChecker:
    def __init__(self, proxy_handler):
        self.proxy_handler = proxy_handler
        self.is_healthy = True
        
    def check_health(self):
        while True:
            try:
                # 测试代理连接
                response = urllib.request.urlopen(
                    'http://www.google.com', 
                    timeout=10
                )
                self.is_healthy = response.status == 200
            except:
                self.is_healthy = False
            
            time.sleep(30)  # 每30秒检查一次
    
    def start(self):
        thread = threading.Thread(target=self.check_health)
        thread.daemon = True
        thread.start()
```

### 自动重启机制
```bash
#!/bin/bash
# auto-restart.sh

while true; do
    if ! pgrep -f "server-vm-proxy.py" > /dev/null; then
        echo "代理服务器已停止，正在重启..."
        python3 server-vm-proxy.py &
    fi
    sleep 60
done
```

## 📈 扩展功能

### 负载均衡
```python
import random

class LoadBalancingProxyHandler(ProxyHandler):
    upstream_proxies = [
        'http://*************:8888',
        'http://192.168.249.8:8888',
        'http://192.168.249.9:8888'
    ]
    
    def get_upstream_proxy(self):
        return random.choice(self.upstream_proxies)
```

### 请求重试机制
```python
import time

class RetryProxyHandler(ProxyHandler):
    max_retries = 3
    retry_delay = 1
    
    def proxy_request_with_retry(self):
        for attempt in range(self.max_retries):
            try:
                return self.proxy_request()
            except Exception as e:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))
                    continue
                raise e
```

这些高级配置可以根据您的具体需求进行调整和实施。
