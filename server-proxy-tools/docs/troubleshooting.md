# 故障排除指南

## 🔍 常见问题及解决方案

### 1. 连接被拒绝 (Connection refused)

**症状：**
```
curl: (7) Failed to connect to ************* port 8888: Connection refused
```

**可能原因：**
- Mac代理服务器未启动
- 防火墙阻挡
- 端口被占用

**解决方案：**
```bash
# 1. 检查Mac代理服务器是否运行
lsof -i :8888

# 2. 启动Mac代理服务器
python3 mac-proxy-server.py

# 3. 检查防火墙状态（Mac）
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate

# 4. 临时关闭防火墙测试
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setglobalstate off
```

### 2. DNS解析失败

**症状：**
```
curl: (6) Could not resolve host: www.google.com
```

**解决方案：**
```bash
# 1. 使用IP地址测试
curl -I --proxy http://*************:8888 http://*******

# 2. 配置DNS服务器
echo "nameserver *******" | sudo tee -a /etc/resolv.conf

# 3. 使用socks5-hostname参数
curl -I --socks5-hostname *************:1080 https://www.google.com
```

### 3. HTTPS连接失败

**症状：**
```
curl: (35) OpenSSL SSL_connect: SSL_ERROR_SYSCALL
```

**解决方案：**
```bash
# 1. 检查ClashX是否运行
ps aux | grep -i clash
lsof -i :1086

# 2. 测试HTTP连接
curl -I http://www.google.com

# 3. 重启ClashX应用
```

### 4. 虚机无法访问代理

**症状：**
```
curl: (7) Failed to connect to ************* port 3128: Connection refused
```

**解决方案：**
```bash
# 1. 检查服务器上的虚机代理是否运行
lsof -i :3128

# 2. 启动虚机代理服务器
python3 server-vm-proxy.py

# 3. 检查虚机网络配置
ip route show | grep default
```

### 5. Git操作失败

**症状：**
```
fatal: unable to access 'https://github.com/repo.git/': HTTP 500
```

**解决方案：**
```bash
# 1. 配置Git代理
git config --global http.proxy http://*************:3128
git config --global https.proxy http://*************:3128

# 2. 测试Git连接
git ls-remote https://github.com/octocat/Hello-World.git

# 3. 使用HTTP方式clone
git clone http://github.com/用户名/仓库名.git
```

## 🔧 诊断命令

### 网络连接测试
```bash
# 测试端口连通性
telnet ************* 8888
nc -zv ************* 8888

# 测试代理连接
curl -I --proxy http://*************:8888 https://www.google.com

# 查看网络路由
ip route show
netstat -rn
```

### 端口监听检查
```bash
# Mac上检查
lsof -i :8888
lsof -i :1086

# 服务器上检查
lsof -i :3128
ss -tlnp | grep 3128
```

### 进程状态检查
```bash
# 检查ClashX进程
ps aux | grep -i clash

# 检查Python代理进程
ps aux | grep python3
```

## 📝 日志分析

### Mac代理服务器日志
```
CONNECT to github.com:443
CONNECT Error: [Errno 61] Connection refused
```
**说明：** ClashX可能未运行或配置有问题

### 虚机代理服务器日志
```
VM CONNECT to github.com:443
Mac proxy error: HTTP/1.0 500 Internal Server Error
```
**说明：** Mac代理服务器返回错误，检查Mac端配置

## 🚨 紧急恢复

### 恢复网络配置
```bash
# 取消所有代理设置
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY

# 恢复Git配置
git config --global --unset http.proxy
git config --global --unset https.proxy

# 恢复bashrc（如果有备份）
cp ~/.bashrc.backup.* ~/.bashrc
source ~/.bashrc
```

### 重启服务
```bash
# 重启Mac代理服务器
# 按Ctrl+C停止，然后重新运行
python3 mac-proxy-server.py

# 重启虚机代理服务器
# 按Ctrl+C停止，然后重新运行
python3 server-vm-proxy.py
```

## 📞 获取帮助

如果以上方案都无法解决问题，请：

1. 收集错误日志和系统信息
2. 检查网络配置和防火墙设置
3. 确认所有服务的运行状态
4. 尝试最小化配置测试

记住：大多数问题都是由于服务未启动或网络配置错误导致的。
