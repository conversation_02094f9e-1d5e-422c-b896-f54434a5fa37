#!/usr/bin/env python3
"""
服务器端虚机代理服务器
为虚机提供外网访问代理服务，转发请求到Mac代理服务器

使用方法：
    python3 server-vm-proxy.py [MAC_IP]

参数：
    MAC_IP: Mac机器的VPN IP地址，默认从SSH_CLIENT环境变量获取

端口：3128
"""

import http.server
import socketserver
import urllib.request
import socket
import threading
import select
import sys
import os

class VMProxyHandler(http.server.BaseHTTPRequestHandler):
    def __init__(self, *args, mac_ip=None, **kwargs):
        self.mac_ip = mac_ip
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        self.proxy_request()
    
    def do_POST(self):
        self.proxy_request()
    
    def do_HEAD(self):
        self.proxy_request()
    
    def proxy_request(self):
        try:
            # 转发到Mac上的代理服务器
            proxy_handler = urllib.request.ProxyHandler({
                'http': f'http://{self.mac_ip}:8888',
                'https': f'http://{self.mac_ip}:8888'
            })
            opener = urllib.request.build_opener(proxy_handler)
            
            if self.path.startswith('http'):
                url = self.path
            else:
                url = 'http://' + self.headers.get('Host', '') + self.path
            
            req = urllib.request.Request(url, method=self.command)
            
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection', 'proxy-connection']:
                    req.add_header(header, value)
            
            response = opener.open(req, timeout=30)
            
            self.send_response(response.getcode())
            for header, value in response.headers.items():
                if header.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(header, value)
            self.end_headers()
            
            if self.command != 'HEAD':
                self.wfile.write(response.read())
            
        except Exception as e:
            print(f'VM Proxy Error: {e}')
            self.send_error(500, str(e))
    
    def do_CONNECT(self):
        try:
            host, port = self.path.split(':')
            port = int(port)
            
            print(f'VM CONNECT to {host}:{port}')
            
            # 通过Mac代理建立CONNECT隧道
            proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            proxy_socket.connect((self.mac_ip, 8888))
            
            # 发送CONNECT请求到Mac代理
            connect_request = f'CONNECT {host}:{port} HTTP/1.1\r\nHost: {host}:{port}\r\n\r\n'
            proxy_socket.send(connect_request.encode())
            
            # 读取Mac代理响应
            response = proxy_socket.recv(4096).decode()
            if '200' not in response:
                raise Exception(f'Mac proxy error: {response}')
            
            # 发送成功响应给虚机
            self.send_response(200, 'Connection established')
            self.end_headers()
            
            # 开始隧道转发
            client_socket = self.connection
            
            def tunnel_data():
                try:
                    while True:
                        ready, _, _ = select.select([client_socket, proxy_socket], [], [], 60)
                        if not ready:
                            break
                        
                        for sock in ready:
                            try:
                                data = sock.recv(4096)
                                if not data:
                                    return
                                
                                if sock is client_socket:
                                    proxy_socket.send(data)
                                else:
                                    client_socket.send(data)
                            except:
                                return
                except Exception as e:
                    print(f'VM Tunnel error: {e}')
                finally:
                    try:
                        proxy_socket.close()
                    except:
                        pass
            
            tunnel_data()
            
        except Exception as e:
            print(f'VM CONNECT Error: {e}')
            try:
                self.send_error(500, str(e))
            except:
                pass

class ThreadedTCPServer(socketserver.ThreadingMixIn, socketserver.TCPServer):
    allow_reuse_address = True

def get_mac_ip():
    """从SSH_CLIENT环境变量获取Mac的IP地址"""
    ssh_client = os.environ.get('SSH_CLIENT', '')
    if ssh_client:
        return ssh_client.split()[0]
    return None

def main():
    PORT = 3128
    
    # 获取Mac IP地址
    if len(sys.argv) > 1:
        mac_ip = sys.argv[1]
    else:
        mac_ip = get_mac_ip()
        if not mac_ip:
            print("❌ 无法获取Mac IP地址")
            print("请手动指定: python3 server-vm-proxy.py <MAC_IP>")
            sys.exit(1)
    
    print("=" * 60)
    print("服务器端虚机代理服务器启动中...")
    print(f"监听端口: {PORT}")
    print(f"上游代理: {mac_ip}:8888")
    print("功能: 为虚机提供外网访问代理")
    print("=" * 60)
    
    # 创建处理器工厂函数
    def handler_factory(*args, **kwargs):
        return VMProxyHandler(*args, mac_ip=mac_ip, **kwargs)
    
    try:
        with ThreadedTCPServer(('0.0.0.0', PORT), handler_factory) as httpd:
            print(f'✅ 虚机代理服务器运行在 0.0.0.0:{PORT}')
            print('📝 虚机配置命令:')
            print('   export http_proxy=http://*************:3128')
            print('   export https_proxy=http://*************:3128')
            print('')
            print('🔄 按 Ctrl+C 停止服务器')
            print('=' * 60)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print('\n🛑 虚机代理服务器已停止')
    except Exception as e:
        print(f'❌ 启动失败: {e}')
        sys.exit(1)

if __name__ == '__main__':
    main()
