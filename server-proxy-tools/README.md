# 服务器及虚机外网访问代理工具

## 🎯 项目简介
让无法直接访问外网的服务器和虚机，通过本地Mac机器实现Google、GitHub等外网访问的完整解决方案。

## 🏗️ 网络架构

```
虚机们(192.168.122.x) 
    ↓ HTTP/HTTPS代理: 192.168.122.1:3128
服务器(172.25.21.1) 
    ↓ HTTP/HTTPS代理: 192.168.249.7:8888  
本地Mac(192.168.249.7) 
    ↓ 通过ClashX智能代理
外网(Google、GitHub等)
```

## 📁 文件结构

```
server-proxy-tools/
├── README.md                    # 本文档
├── mac-proxy-server.py          # Mac端代理服务器
├── server-vm-proxy.py           # 服务器端虚机代理服务器
├── setup-server.sh              # 服务器配置脚本
├── setup-vm.sh                  # 虚机配置脚本
└── docs/
    ├── troubleshooting.md        # 故障排除指南
    ├── advanced-config.md        # 高级配置说明
    └── implementation-details.md # 内部实现原理详解
```

## 🚀 快速开始

### 第一步：启动Mac代理服务器
```bash
cd server-proxy-tools
python3 mac-proxy-server.py
```

### 第二步：配置服务器
```bash
# 在服务器上执行
./setup-server.sh
```

### 第三步：配置虚机（可选）
```bash
# 在服务器上启动虚机代理
python3 server-vm-proxy.py

# 在虚机中执行
./setup-vm.sh
```

## ✅ 验证测试

### 服务器测试
```bash
curl -I https://www.google.com
curl -I https://github.com
git clone https://github.com/用户名/仓库名.git
```

### 虚机测试
```bash
curl -I https://www.google.com
curl -I https://github.com
```

## 🔧 关键端口

- **8888**：Mac代理服务器端口（服务器访问）
- **3128**：服务器代理端口（虚机访问）
- **1086**：ClashX HTTP代理端口（Mac本地）

## 📋 支持功能

- ✅ HTTP/HTTPS网站访问
- ✅ Git clone/push操作
- ✅ 包管理器安装（pip、yum、apt等）
- ✅ 文件下载（wget、curl）
- ✅ 任何需要网络的应用

## 🆘 故障排除

遇到问题请查看 [故障排除指南](docs/troubleshooting.md)

## ⚙️ 高级配置

更多配置选项请查看 [高级配置说明](docs/advanced-config.md)

## 🔍 实现原理

了解内部工作机制请查看 [实现原理详解](docs/implementation-details.md)

## 📝 使用说明

1. **启动顺序**：先启动Mac代理，再配置服务器，最后配置虚机
2. **保持连接**：Mac代理服务器需要保持运行状态
3. **网络要求**：确保Mac可以访问外网且ClashX正常工作

## 🔄 版本历史

- v1.0.0 - 初始版本，支持基本的HTTP/HTTPS代理功能
