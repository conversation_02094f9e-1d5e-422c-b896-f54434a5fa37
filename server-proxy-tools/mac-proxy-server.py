#!/usr/bin/env python3
"""
Mac端代理服务器
为服务器提供外网访问代理服务，自动利用本地ClashX等代理配置

使用方法：
    python3 mac-proxy-server.py

端口：8888
"""

import http.server
import socketserver
import urllib.request
import socket
import threading
import select
import sys

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.proxy_request()
    
    def do_POST(self):
        self.proxy_request()
    
    def do_HEAD(self):
        self.proxy_request()
    
    def proxy_request(self):
        try:
            if self.path.startswith('http'):
                url = self.path
            else:
                url = 'http://' + self.headers.get('Host', '') + self.path
            
            req = urllib.request.Request(url, method=self.command)
            
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection', 'proxy-connection']:
                    req.add_header(header, value)
            
            # 使用系统默认的网络配置（包括ClashX）
            response = urllib.request.urlopen(req, timeout=30)
            
            self.send_response(response.getcode())
            for header, value in response.headers.items():
                if header.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(header, value)
            self.end_headers()
            
            if self.command != 'HEAD':
                self.wfile.write(response.read())
            
        except Exception as e:
            print(f'HTTP Error: {e}')
            self.send_error(500, str(e))
    
    def do_CONNECT(self):
        try:
            host, port = self.path.split(':')
            port = int(port)
            
            print(f'CONNECT to {host}:{port}')
            
            target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            target_socket.settimeout(30)
            
            try:
                # 先尝试直接连接
                target_socket.connect((host, port))
            except:
                # 如果直接连接失败，尝试通过ClashX代理
                target_socket.close()
                target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                target_socket.connect(('127.0.0.1', 1086))
                
                connect_request = f'CONNECT {host}:{port} HTTP/1.1\r\nHost: {host}:{port}\r\n\r\n'
                target_socket.send(connect_request.encode())
                
                response = target_socket.recv(4096).decode()
                if '200' not in response:
                    raise Exception(f'Proxy connection failed: {response}')
            
            self.send_response(200, 'Connection established')
            self.end_headers()
            
            client_socket = self.connection
            
            def tunnel_data():
                try:
                    while True:
                        ready, _, _ = select.select([client_socket, target_socket], [], [], 60)
                        if not ready:
                            break
                        
                        for sock in ready:
                            try:
                                data = sock.recv(4096)
                                if not data:
                                    return
                                
                                if sock is client_socket:
                                    target_socket.send(data)
                                else:
                                    client_socket.send(data)
                            except:
                                return
                except Exception as e:
                    print(f'Tunnel error: {e}')
                finally:
                    try:
                        target_socket.close()
                    except:
                        pass
            
            tunnel_data()
            
        except Exception as e:
            print(f'CONNECT Error: {e}')
            try:
                self.send_error(500, str(e))
            except:
                pass

class ThreadedTCPServer(socketserver.ThreadingMixIn, socketserver.TCPServer):
    allow_reuse_address = True

def main():
    PORT = 8888
    
    print("=" * 60)
    print("Mac端代理服务器启动中...")
    print(f"监听端口: {PORT}")
    print("功能: 为服务器提供外网访问代理")
    print("支持: HTTP/HTTPS/CONNECT协议")
    print("=" * 60)
    
    try:
        with ThreadedTCPServer(('0.0.0.0', PORT), ProxyHandler) as httpd:
            print(f'✅ 代理服务器运行在 0.0.0.0:{PORT}')
            print('📝 服务器配置命令:')
            print('   export http_proxy=http://您的VPN_IP:8888')
            print('   export https_proxy=http://您的VPN_IP:8888')
            print('')
            print('🔄 按 Ctrl+C 停止服务器')
            print('=' * 60)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print('\n🛑 代理服务器已停止')
    except Exception as e:
        print(f'❌ 启动失败: {e}')
        sys.exit(1)

if __name__ == '__main__':
    main()
