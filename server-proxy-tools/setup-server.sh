#!/bin/bash
#
# 服务器代理配置脚本
# 自动配置服务器的代理环境变量
#
# 使用方法：
#     ./setup-server.sh [MAC_IP]
#
# 参数：
#     MAC_IP: Mac机器的VPN IP地址，默认从SSH_CLIENT环境变量获取
#

set -uo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取Mac IP地址
get_mac_ip() {
    if [[ $# -gt 0 ]]; then
        echo "$1"
    elif [[ -n "${SSH_CLIENT:-}" ]]; then
        echo "${SSH_CLIENT%% *}"
    else
        return 1
    fi
}

# 测试代理连接
test_proxy() {
    local proxy_url="$1"
    log "测试代理连接: $proxy_url"
    
    if curl -I --connect-timeout 10 --proxy "$proxy_url" https://www.google.com >/dev/null 2>&1; then
        log "✅ 代理连接测试成功"
        return 0
    else
        error "❌ 代理连接测试失败"
        return 1
    fi
}

# 配置代理环境变量
setup_proxy() {
    local mac_ip="$1"
    local proxy_url="http://${mac_ip}:8888"
    
    log "配置代理环境变量..."
    
    # 设置当前会话的代理
    export http_proxy="$proxy_url"
    export https_proxy="$proxy_url"
    export HTTP_PROXY="$proxy_url"
    export HTTPS_PROXY="$proxy_url"
    
    log "当前会话代理已配置: $proxy_url"
    
    # 询问是否永久配置
    echo
    read -p "是否永久配置代理到 ~/.bashrc? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "配置永久代理..."
        
        # 备份原始bashrc
        if [[ -f ~/.bashrc ]]; then
            cp ~/.bashrc ~/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
            log "已备份 ~/.bashrc"
        fi
        
        # 添加代理配置
        cat >> ~/.bashrc << EOF

# 代理配置 - 由 setup-server.sh 添加
export http_proxy="$proxy_url"
export https_proxy="$proxy_url"
export HTTP_PROXY="$proxy_url"
export HTTPS_PROXY="$proxy_url"
EOF
        
        log "✅ 永久代理配置已添加到 ~/.bashrc"
        log "下次登录时自动生效，或执行: source ~/.bashrc"
    fi
}

# 测试网络访问
test_network() {
    log "测试网络访问..."
    
    echo
    log "测试 Google 访问:"
    if curl -I --connect-timeout 10 https://www.google.com 2>/dev/null | head -1; then
        log "✅ Google 访问成功"
    else
        error "❌ Google 访问失败"
    fi
    
    echo
    log "测试 GitHub 访问:"
    if curl -I --connect-timeout 10 https://github.com 2>/dev/null | head -1; then
        log "✅ GitHub 访问成功"
    else
        error "❌ GitHub 访问失败"
    fi
}

# 显示使用说明
show_usage() {
    echo
    log "代理配置完成！"
    echo
    echo -e "${BLUE}常用测试命令:${NC}"
    echo "  curl -I https://www.google.com"
    echo "  curl -I https://github.com"
    echo "  git clone https://github.com/用户名/仓库名.git"
    echo "  pip install 包名"
    echo
    echo -e "${BLUE}取消代理:${NC}"
    echo "  unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY"
    echo
}

# 主函数
main() {
    echo "============================================================"
    log "服务器代理配置脚本"
    echo "============================================================"
    
    # 获取Mac IP
    if ! mac_ip=$(get_mac_ip "$@"); then
        error "无法获取Mac IP地址"
        error "请手动指定: $0 <MAC_IP>"
        exit 1
    fi
    
    log "检测到Mac IP: $mac_ip"
    
    # 测试代理连接
    if ! test_proxy "http://${mac_ip}:8888"; then
        error "请确保Mac上的代理服务器正在运行:"
        error "  python3 mac-proxy-server.py"
        exit 1
    fi
    
    # 配置代理
    setup_proxy "$mac_ip"
    
    # 测试网络访问
    test_network
    
    # 显示使用说明
    show_usage
    
    log "🎉 配置完成！"
}

# 执行主函数
main "$@"
