[{"name": "poc08-culinux-packer", "host": "***********", "protocol": "sftp", "port": 22, "username": "dbgroupuser", "password": "Mob}v~@p5in_5w{\\", "remotePath": "/home/<USER>/culinux-packer/", "uploadOnSave": true, "downloadOnOpen": false, "context": "culinux-packer", "ignore": [".vscode", ".git", "**/.DS_Store", "**/node_modules/**", "**/*.log", "**/tmp/**", "output-*/**"], "watcher": {"files": "**/*", "autoUpload": true, "autoDelete": true}, "syncOption": {"delete": false, "skipCreate": false, "ignoreExisting": false, "update": true}}]