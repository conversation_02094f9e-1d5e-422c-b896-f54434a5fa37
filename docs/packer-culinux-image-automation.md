# 使用 Packer 自动化制作 CULinux 4.0 操作系统镜像

## 1. 概述

本文档详细介绍了如何使用 Packer 自动化制作基于 OpenEuler 22.03 的 CULinux 4.0 操作系统镜像的完整流程。

## 2. 前期准备

### 软硬件环境
- **服务器**: x86_64 架构
- **操作系统**: Linux（CentOS/RHEL/OpenEuler）
- **必备软件**:
  - QEMU/KVM（版本 6.2+）
  - Packer（版本 1.8+）
  - 虚拟化管理工具（libvirt 等）

### 获取安装介质
- **CULinux 4.0 ISO 镜像**: `culinux-4.0-x86_64.iso`
- **YUM 源地址**: `http://************:9006/repository/culinux/`
- **GPG 密钥**: `http://************:9006/repository/culinux/RPM-GPG-KEY-culinux`

## 3. 项目目录结构

```
culinux-project/
├── culinux.pkr.hcl          # 主Packer配置文件
├── http/
│   └── ks.cfg               # Kickstart自动安装配置
├── scripts/
│   ├── cleanup.sh           # 清理脚本
│   └── setup-repos.sh       # 仓库配置脚本
└── README.md                # 项目说明文档
```

## 4. 配置文件详解

### 主配置文件（culinux.pkr.hcl）

```hcl
packer {
  required_plugins {
    qemu = {
      version = ">= 1.0.9"
      source  = "github.com/hashicorp/qemu"
    }
  }
}

source "qemu" "culinux" {
  // 基本配置
  iso_url           = "culinux-4.0-x86_64.iso"
  iso_checksum      = "none"  // 实际使用时应添加ISO校验和
  output_directory  = "output-culinux"
  vm_name           = "culinux-4.0-base.qcow2"
  headless          = true
  accelerator       = "kvm"

  // 虚拟机硬件配置
  cpus              = 4
  memory            = 4096
  disk_size         = "25G"    // CULinux可能需要更大空间
  format            = "qcow2"
  disk_interface    = "virtio"
  net_device        = "virtio-net"

  // QEMU配置
  qemu_binary       = "/usr/libexec/qemu-kvm"
  
  // HTTP服务配置
  http_directory    = "http"
  http_port_min     = 8000
  http_port_max     = 8100

  // 启动配置 - 适配OpenEuler/CULinux引导
  boot_wait         = "10s"
  boot_command = [
    "<tab>",
    " inst.ks=http://{{ .HTTPIP }}:{{ .HTTPPort }}/ks.cfg",
    " inst.text console=tty0 console=ttyS0,115200n8",
    "<enter><wait>"
  ]

  // SSH连接配置
  ssh_username      = "root"
  ssh_password      = "culinux@2025"
  ssh_timeout       = "30m"
  ssh_port          = 22

  // 关机配置
  shutdown_command  = "echo 'culinux@2025' | sudo -S shutdown -P now"
  shutdown_timeout  = "15m"
  
  // QEMU运行参数
  qemuargs = [
    ["-m", "4096M"],
    ["-smp", "4"],
    ["-display", "vnc=:9"],
    ["-serial", "stdio"]
  ]
}

build {
  sources = ["source.qemu.culinux"]
  
  // 执行自定义脚本
  provisioner "shell" {
    scripts = [
      "scripts/setup-repos.sh",
      "scripts/cleanup.sh"
    ]
  }
}
```

### Kickstart 文件（http/ks.cfg）

```bash
# CULinux 4.0 Kickstart 配置文件
# 基于 OpenEuler 22.03

# 系统语言和键盘
lang zh_CN.UTF-8
keyboard --vckeymap=us --xlayouts='us'

# 时区设置
timezone Asia/Shanghai --isUtc

# 安装模式
install
text
reboot

# 网络配置
network --bootproto=dhcp --device=ens3 --onboot=on --ipv6=auto --activate
network --hostname=culinux-base

# 安全配置
firewall --enabled --ssh
selinux --disabled
authselect --enableshadow --passalgo=sha512

# 用户配置
rootpw --plaintext culinux@2025

# 磁盘分区
clearpart --all --initlabel
# 使用 LVM 分区方案
part /boot --fstype="xfs" --size=1024
part pv.01 --fstype="lvmpv" --size=1 --grow
volgroup cl pv.01
logvol / --fstype="xfs" --name=root --vgname=cl --size=1 --grow
logvol swap --fstype="swap" --name=swap --vgname=cl --size=2048

# 引导加载器
bootloader --location=mbr --boot-drive=vda

# 软件包选择
%packages --ignoremissing --excludedocs
@core
@base
# 云计算相关
cloud-init
cloud-utils-growpart
# 网络工具
openssh-server
openssh-clients
net-tools
wget
curl
# 系统工具
vim
nano
htop
tree
rsync
# 开发工具
gcc
make
git
# Python环境
python3
python3-pip
%end

# 安装前脚本
%pre
# 记录安装开始时间
echo "CULinux 4.0 installation started at $(date)" > /tmp/install.log
%end

# 安装后脚本
%post --log=/root/kickstart-post.log

# 设置root密码
echo 'root:culinux@2025' | chpasswd

# SSH配置优化
sed -i 's/^#PermitRootLogin.*/PermitRootLogin yes/' /etc/ssh/sshd_config
sed -i 's/^PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config
sed -i 's/^#PubkeyAuthentication.*/PubkeyAuthentication yes/' /etc/ssh/sshd_config

# 网络配置
systemctl enable NetworkManager
nmcli connection modify 'System ens3' connection.autoconnect yes

# 启用必要服务
systemctl enable sshd
systemctl enable cloud-init
systemctl enable cloud-config
systemctl enable cloud-final
systemctl enable cloud-init-local

# 导入CULinux GPG密钥
curl -o /etc/pki/rpm-gpg/RPM-GPG-KEY-culinux http://************:9006/repository/culinux/RPM-GPG-KEY-culinux
rpm --import /etc/pki/rpm-gpg/RPM-GPG-KEY-culinux

# 配置CULinux YUM源
cat > /etc/yum.repos.d/culinux.repo << 'EOF'
[culinux-base]
name=CULinux 4.0 Base Repository
baseurl=http://************:9006/repository/culinux/base/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-culinux
enabled=1
priority=1

[culinux-updates]
name=CULinux 4.0 Updates Repository
baseurl=http://************:9006/repository/culinux/updates/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-culinux
enabled=1
priority=1

[culinux-extras]
name=CULinux 4.0 Extras Repository
baseurl=http://************:9006/repository/culinux/extras/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-culinux
enabled=1
priority=2
EOF

# 备份原有的OpenEuler源
if [ -f /etc/yum.repos.d/openEuler.repo ]; then
    mv /etc/yum.repos.d/openEuler.repo /etc/yum.repos.d/openEuler.repo.bak
fi

# 清理YUM缓存并更新
yum clean all
yum makecache

# Cloud-init配置 - 适配OpenStack
mkdir -p /etc/cloud/cloud.cfg.d/
cat > /etc/cloud/cloud.cfg.d/99_openstack.cfg << 'EOF'
datasource_list: [ OpenStack, ConfigDrive, NoCloud, None ]
datasource:
  OpenStack:
    metadata_urls: [ 'http://169.254.169.254' ]
    timeout: 5
    max_wait: 120
    retries: 3
  ConfigDrive:
    dsmode: local
EOF

# 优化cloud-init配置
cat > /etc/cloud/cloud.cfg.d/90_dpkg.cfg << 'EOF'
# to update this file, run dpkg-reconfigure cloud-init
datasource_list: [ OpenStack, ConfigDrive, NoCloud, None ]
EOF

# 设置系统时区
timedatectl set-timezone Asia/Shanghai

# 创建必要的目录
mkdir -p /var/log/cloud-init

# 记录安装完成信息
echo "CULinux 4.0 installation completed at $(date)" >> /root/kickstart-post.log
echo "System ready for cloud deployment" >> /root/kickstart-post.log

%end
```

### 仓库配置脚本（scripts/setup-repos.sh）

```bash
#!/bin/bash
# CULinux 4.0 仓库配置和系统优化脚本

set -e

echo "开始配置 CULinux 4.0 系统..."

# 更新系统时间
timedatectl set-timezone Asia/Shanghai
chrony sources -v

# 验证GPG密钥
echo "验证GPG密钥..."
rpm -qa gpg-pubkey --qf '%{name}-%{version}-%{release} --> %{summary}\n'

# 测试YUM源连接
echo "测试YUM源连接..."
yum repolist enabled

# 更新系统软件包
echo "更新系统软件包..."
yum update -y --skip-broken

# 安装额外的云计算工具
echo "安装云计算相关工具..."
yum install -y cloud-utils cloud-utils-growpart

# 安装监控和管理工具
echo "安装系统管理工具..."
yum install -y \
    htop \
    iotop \
    nethogs \
    tcpdump \
    strace \
    lsof \
    tree

# 配置系统优化参数
echo "配置系统优化参数..."

# 内核参数优化
cat >> /etc/sysctl.conf << 'EOF'
# CULinux 4.0 系统优化参数
vm.swappiness = 10
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
EOF

# 配置文件句柄限制
cat >> /etc/security/limits.conf << 'EOF'
# CULinux 4.0 文件句柄优化
* soft nofile 65536
* hard nofile 65536
* soft nproc 4096
* hard nproc 4096
EOF

# 配置systemd服务限制
mkdir -p /etc/systemd/system.conf.d/
cat > /etc/systemd/system.conf.d/limits.conf << 'EOF'
[Manager]
DefaultLimitNOFILE=65536
DefaultLimitNPROC=4096
EOF

echo "CULinux 4.0 系统配置完成"
```

### 清理脚本（scripts/cleanup.sh）

```bash
#!/bin/bash
# CULinux 4.0 镜像清理脚本

set -e

echo "开始清理 CULinux 4.0 镜像..."

# 清理包管理器缓存
echo "清理YUM缓存..."
yum clean all
rm -rf /var/cache/yum/*
rm -rf /var/cache/dnf/*

# 清理RPM数据库临时文件
echo "清理RPM临时文件..."
rm -rf /var/lib/rpm/__db*

# 清理临时文件和日志
echo "清理临时文件..."
rm -rf /tmp/*
rm -rf /var/tmp/*
rm -rf /root/.cache/*

# 清理系统日志
echo "清理系统日志..."
journalctl --flush
journalctl --rotate
journalctl --vacuum-time=1s

# 清理传统日志文件
find /var/log -type f -name "*.log" -exec truncate -s 0 {} \;
find /var/log -type f -name "*.log.*" -delete
truncate -s 0 /var/log/messages
truncate -s 0 /var/log/lastlog
truncate -s 0 /var/log/wtmp
truncate -s 0 /var/log/btmp

# 清理SSH相关文件
echo "清理SSH配置..."
rm -f /etc/ssh/ssh_host_*
rm -rf /root/.ssh/
rm -f /home/<USER>/.ssh/authorized_keys 2>/dev/null || true

# 清理网络配置
echo "清理网络配置..."
rm -f /etc/udev/rules.d/70-persistent-net.rules
rm -f /etc/udev/rules.d/75-persistent-net-generator.rules

# 清理机器ID
echo "清理机器标识..."
> /etc/machine-id
> /var/lib/dbus/machine-id

# 清理cloud-init状态
echo "清理cloud-init状态..."
rm -rf /var/lib/cloud/*
rm -rf /var/log/cloud-init*

# 清理命令历史
echo "清理命令历史..."
history -c
> /root/.bash_history
> /root/.viminfo

# 清理Python缓存
echo "清理Python缓存..."
find / -name "*.pyc" -delete 2>/dev/null || true
find / -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 清理内核模块缓存
echo "清理内核缓存..."
rm -rf /lib/modules/*/kernel/drivers/gpu/drm/nouveau/
depmod -a

# 零化剩余空间（可选，会增加镜像制作时间）
echo "优化磁盘空间..."
dd if=/dev/zero of=/EMPTY bs=1M 2>/dev/null || true
rm -f /EMPTY

# 同步文件系统
sync

echo "CULinux 4.0 镜像清理完成"
```

## 5. 构建过程

### 准备工作

```bash
# 创建项目目录
mkdir -p culinux-project/{http,scripts}
cd culinux-project

# 下载CULinux 4.0 ISO镜像（请替换为实际下载地址）
# wget http://your-culinux-mirror/culinux-4.0-x86_64.iso

# 验证ISO文件
ls -la culinux-4.0-x86_64.iso
```

### 构建命令

```bash
# 验证配置文件语法
packer validate culinux.pkr.hcl

# 开始构建（基本模式）
packer build culinux.pkr.hcl

# 调试模式构建
packer build -debug -on-error=ask culinux.pkr.hcl

# 并行构建（如果有多个配置）
packer build -parallel-builds=2 culinux.pkr.hcl
```

### 监控构建过程

```bash
# 通过VNC查看安装过程（端口5909）
vncviewer localhost:5909

# 查看构建日志
tail -f /tmp/packer-build.log
```

## 6. 验证和测试

### 本地验证

```bash
# 启动生成的镜像进行测试
/usr/libexec/qemu-kvm \
  -m 4096 \
  -smp 4 \
  -drive file=output-culinux/culinux-4.0-base.qcow2,format=qcow2 \
  -net nic,model=virtio \
  -net user,hostfwd=tcp::2222-:22 \
  -enable-kvm \
  -display vnc=:10

# SSH连接测试
ssh -p 2222 root@localhost
```

### 功能验证脚本

```bash
#!/bin/bash
# CULinux 4.0 镜像功能验证脚本

echo "=== CULinux 4.0 系统信息验证 ==="

# 系统版本信息
echo "系统版本:"
cat /etc/os-release

# 内核版本
echo "内核版本:"
uname -a

# YUM源配置验证
echo "YUM源配置:"
yum repolist enabled

# 已安装软件包验证
echo "关键软件包:"
rpm -qa | grep -E "(cloud-init|openssh|python3)" | sort

# 网络配置验证
echo "网络配置:"
ip addr show
systemctl status NetworkManager

# 服务状态验证
echo "关键服务状态:"
systemctl status sshd cloud-init --no-pager

# Cloud-init配置验证
echo "Cloud-init配置:"
cloud-init --version
ls -la /etc/cloud/cloud.cfg.d/

echo "=== 验证完成 ==="
```

## 7. 部署和使用

### 上传到云平台

```bash
# 压缩镜像以节省上传时间
qemu-img convert -c -f qcow2 -O qcow2 \
  output-culinux/culinux-4.0-base.qcow2 \
  culinux-4.0-compressed.qcow2

# 上传到OpenStack Glance
openstack image create \
  --container-format bare \
  --disk-format qcow2 \
  --file culinux-4.0-compressed.qcow2 \
  --property os_distro=culinux \
  --property os_version=4.0 \
  --property hw_qemu_guest_agent=yes \
  --property hw_disk_bus=virtio \
  --property hw_vif_model=virtio \
  --min-disk 8 \
  --min-ram 1024 \
  "CULinux-4.0-Base"
```

### libvirt集成

```bash
# 复制镜像到libvirt存储池
cp output-culinux/culinux-4.0-base.qcow2 /var/lib/libvirt/images/
chown qemu:qemu /var/lib/libvirt/images/culinux-4.0-base.qcow2

# 创建虚拟机
virt-install \
  --name culinux-test \
  --memory 4096 \
  --vcpus 4 \
  --disk path=/var/lib/libvirt/images/culinux-4.0-base.qcow2,format=qcow2 \
  --import \
  --os-variant rhel8.0 \
  --network default \
  --graphics vnc,listen=0.0.0.0 \
  --noautoconsole
```

## 8. 故障排除

### 常见问题

#### 1. YUM源连接失败
```bash
# 检查网络连通性
ping ************
telnet ************ 9006

# 检查GPG密钥
curl -I http://************:9006/repository/culinux/RPM-GPG-KEY-culinux
```

#### 2. Kickstart安装失败
```bash
# 检查Kickstart语法
ksvalidator http/ks.cfg

# 查看安装日志
# VNC连接后查看 /tmp/anaconda.log
```

#### 3. SSH连接超时
```bash
# 检查SSH服务状态
systemctl status sshd

# 检查防火墙配置
firewall-cmd --list-all
```

### 调试技巧

1. **启用详细日志**: 在boot_command中添加`inst.loglevel=debug`
2. **保留失败的VM**: 使用`-on-error=ask`参数
3. **VNC监控**: 通过VNC实时观察安装过程
4. **网络抓包**: 使用tcpdump监控网络通信

## 9. 最佳实践

### 安全配置
- 定期更新基础镜像
- 最小化软件包安装
- 启用必要的安全功能
- 清理敏感信息

### 性能优化
- 使用virtio驱动
- 合理配置内存和CPU
- 启用BBR拥塞控制算法

### 自动化集成
- 结合CI/CD流水线
- 自动化测试验证
- 版本标签管理

---

**文档版本**: v1.0  
**创建时间**: 2025-07-30  
**维护人员**: zhaoj296  
**适用范围**: CULinux 4.0 (基于OpenEuler 22.03)
