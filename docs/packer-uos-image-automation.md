# 使用 Packer 自动化制作统信 UOS 操作系统镜像

## 1. 概述

本文档详细介绍了如何使用 Packer 自动化制作统信 UOS 操作系统镜像的完整流程，包括环境准备、配置文件编写、构建过程和验证方法。

## 2. 前期准备

### 软硬件环境
- **服务器**: x86_64 架构
- **操作系统**: Linux（CentOS/RHEL/UOS）
- **必备软件**:
  - QEMU/KVM（版本 6.2+）
  - Packer（版本 1.8+）
  - 虚拟化管理工具（libvirt 等）

### 获取安装介质
- **统信 UOS 服务器版镜像**: `uos-server-20-1060e-amd64.iso`

## 3. Packer 配置文件结构

### 项目目录结构
```
project/
├── uos-openeuler.pkr.hcl    # 主Packer配置文件
├── http/
│   └── ks.cfg               # Kickstart自动安装配置
└── scripts/
    └── cleanup.sh           # 清理脚本
```

## 4. 配置详解

### 主配置文件（uos-openeuler.pkr.hcl）

```hcl
packer {
  required_plugins {
    qemu = {
      version = ">= 1.0.9"
      source  = "github.com/hashicorp/qemu"
    }
  }
}

source "qemu" "uos-openeuler" {
  // 基本配置
  iso_url           = "uos-server-20-1060e-amd64.iso"
  iso_checksum      = "none"  // 实际使用时应添加校验和
  output_directory  = "output-uos"
  vm_name           = "uos-openeuler-base.qcow2"
  headless          = true
  accelerator       = "kvm"

  // 虚拟机配置
  cpus              = 4
  memory            = 4096
  disk_size         = "20G"
  format            = "qcow2"
  disk_interface    = "virtio"
  net_device        = "virtio-net"

  qemu_binary = "/usr/libexec/qemu-kvm"  // QEMU 二进制路径

  // HTTP目录用于提供自动安装文件
  http_directory    = "http"

  // 启动和关机配置
  boot_wait         = "10s"
  boot_command = [
    "<esc>",
    "vmlinuz initrd=initrd.img inst.ks=http://{{ .HTTPIP }}:{{ .HTTPPort }}/ks.cfg inst.text",
    "<enter>"
  ]

  shutdown_command  = "echo 'wo1trove2cloud#mysql$' | sudo -S shutdown -P now"
  shutdown_timeout  = "15m"

  // SSH配置
  ssh_username      = "root"
  ssh_password      = "wo1trove2cloud#mysql$"
  ssh_timeout       = "30m"
  ssh_port          = 22
  
  // QEMU参数
  qemuargs          = [
    ["-m", "4096M"],
    ["-smp", "4"],
    ["-display", "vnc=:8"]
  ]
}

build {
  sources = ["source.qemu.uos-openeuler"]
}
```

### Kickstart 文件（http/ks.cfg）

```bash
# Kickstart 配置文件 - 适用于 OpenEuler 兼容版本的统信 UOS
lang zh_CN.UTF-8
keyboard us
timezone Asia/Shanghai --isUtc

# 接受许可协议
eula --agreed

# 安装方式
install
text
reboot

# 网络配置
network --bootproto=dhcp --device=eth0 --onboot=on --ipv6=auto
network --hostname=uos-base

# 安全选项
firewall --enabled --ssh
selinux --permissive
authselect --enableshadow --passalgo=sha512

# 系统服务
services --enabled="chronyd,sshd,cloud-init,cloud-config,cloud-final,cloud-init-local"

# 用户配置
rootpw --plaintext wo1trove2cloud#mysql$

# 分区配置
clearpart --all --initlabel
autopart --type=lvm

# 软件包配置
%packages --ignoremissing
@core
cloud-init
cloud-utils-growpart
openssh-server
vim
net-tools
%end

# 安装后脚本
%post
# 确保将root密码正确设置
echo 'root:wo1trove2cloud#mysql$' | chpasswd

# 启用SSH密码认证
sed -i 's/^PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config
sed -i 's/^#PermitRootLogin.*/PermitRootLogin yes/' /etc/ssh/sshd_config

# 确保网络自动启动
nmcli connection modify 'System eth0' connection.autoconnect yes

# 预先创建cloud-init配置目录
mkdir -p /etc/cloud/cloud.cfg.d/

# 设置cloud-init为OpenStack
cat > /etc/cloud/cloud.cfg.d/99_openstack.cfg << EOF
datasource_list: [ OpenStack ]
datasource:
  OpenStack:
    metadata_urls: [ 'http://169.254.169.254' ]
    timeout: 5
    max_wait: 120
EOF

# 设置公司YUM源
cat > /etc/yum.repos.d/wocloud.repo << EOF
[base-production]
name=Wocloud Base
baseurl=http://***********:38888/production/CULinux/x86_64/base
gpgcheck=0
enabled=1

[extra-production]
name=Wocloud extra
baseurl=http://***********:38888/production/CULinux/x86_64/extra
gpgcheck=0
enabled=1
EOF

# 备份并禁用默认YUM源
mv /etc/yum.repos.d/CentOS*.repo /etc/yum.repos.d/CentOS.repo.bak 2>/dev/null || true
mv /etc/yum.repos.d/epel*.repo /etc/yum.repos.d/epel.repo.bak 2>/dev/null || true
mv /etc/yum.repos.d/uos*.repo /etc/yum.repos.d/uos.repo.bak 2>/dev/null || true
%end
```

### 清理脚本（scripts/cleanup.sh）

```bash
#!/bin/bash
# 镜像清理脚本

set -e

echo "开始清理镜像..."

# 清理包管理器缓存
yum clean all
rm -rf /var/cache/yum/*

# 清理临时文件
rm -rf /tmp/*
rm -rf /var/tmp/*

# 清理日志文件
truncate -s 0 /var/log/messages
truncate -s 0 /var/log/lastlog
truncate -s 0 /var/log/wtmp
truncate -s 0 /var/log/btmp
find /var/log -name "*.log" -exec truncate -s 0 {} \;

# 清理SSH密钥
rm -f /etc/ssh/ssh_host_*
rm -f /root/.ssh/authorized_keys

# 清理机器ID
> /etc/machine-id
> /var/lib/dbus/machine-id

# 清理网络配置
rm -f /etc/udev/rules.d/70-persistent-net.rules

# 清理cloud-init缓存
rm -rf /var/lib/cloud/*

# 清理命令历史
history -c
> /root/.bash_history

echo "镜像清理完成"
```

## 5. 构建过程详解

### 关键配置点剖析

#### 引导方式选择
通过 `<esc>` 进入 isolinux 引导器的 boot prompt，直接传递内核参数：

```hcl
boot_command = [
  "<esc>",
  "vmlinuz initrd=initrd.img inst.ks=http://{{ .HTTPIP }}:{{ .HTTPPort }}/ks.cfg inst.text",
  "<enter>"
]
```

> **注意**: 由于统信 UOS 使用 isolinux 作为 BIOS 引导器，直接使用内核文件名（不加路径前缀），而不是常见的 `linux /path/to/vmlinuz` 格式。

#### VNC 监控
通过指定 VNC 端口，实时监控安装过程：

```hcl
qemuargs = [
  // ...其他参数
  ["-display", "vnc=:8"]  // VNC端口5908
]
```

#### 强化配置与安全实践
- 提前配置 cloud-init，满足云平台需求
- 清空 SSH 密钥，确保云平台可以生成唯一密钥
- 删除机器 ID，避免克隆后 IP 冲突

### 构建命令

```bash
# 基本构建命令
packer build uos-openeuler.pkr.hcl

# 调试模式（如需调试）
packer build -on-error=ask -debug uos-openeuler.pkr.hcl
```

## 6. 常见问题与解决方案

### Kickstart 未被识别
**症状**: 安装程序进入交互界面，而非自动安装。

**解决方案**:
1. 检查 `boot_command` 中的 Kickstart 参数格式
2. 确保 HTTP 服务可访问
3. 调试时可设置 `headless=false` 观察引导过程

### 网络连接问题
**症状**: 无法下载 Kickstart 文件或软件包。

**解决方案**:
1. 检查防火墙设置
2. 验证网络配置
3. 确认 YUM 源可访问性

### SSH 连接超时
**症状**: Packer 无法通过 SSH 连接到虚拟机。

**解决方案**:
1. 检查 SSH 服务是否正常启动
2. 验证密码和用户名配置
3. 检查防火墙是否阻止 SSH 连接

## 7. 镜像验证与使用

### 本地验证
通过 QEMU/KVM 直接启动生成的 qcow2 镜像：

```bash
/usr/libexec/qemu-kvm \
  -m 4096 \
  -smp 4 \
  -drive file=output-uos/uos-openeuler-base.qcow2,format=qcow2 \
  -net nic,model=virtio \
  -net user,hostfwd=tcp::2222-:22 \
  -enable-kvm \
  -display vnc=:8
```

### 集成到虚拟化平台
使用 `virt-install` 命令导入镜像到 libvirt：

```bash
# 复制镜像到指定位置
cp output-uos/uos-openeuler-base.qcow2 /data/kvmpool
chown qemu:qemu /data/kvmpool/uos-openeuler-base.qcow2
chmod 644 /data/kvmpool/uos-openeuler-base.qcow2

# 创建虚拟机
virt-install \
  --name uos-vm \
  --memory 4096 \
  --vcpus 4 \
  --disk path=/data/kvmpool/uos-openeuler-base.qcow2,format=qcow2 \
  --import \
  --os-variant generic \
  --network default \
  --video virtio \
  --graphics vnc,listen=0.0.0.0 \
  --noautoconsole
```

### 云平台集成
将生成的镜像上传到云平台（如 OpenStack）：

```bash
# 上传到 OpenStack Glance
openstack image create \
  --container-format bare \
  --disk-format qcow2 \
  --file output-uos/uos-openeuler-base.qcow2 \
  --property os_distro=uos \
  --property hw_qemu_guest_agent=yes \
  --property hw_disk_bus=virtio \
  --property hw_vif_model=virtio \
  uos-openeuler-base
```

## 8. 最佳实践

### 安全建议
1. **定期更新**: 保持基础镜像的安全补丁最新
2. **最小化安装**: 只安装必要的软件包
3. **清理敏感信息**: 确保镜像中不包含敏感数据
4. **启用安全功能**: 配置防火墙、SELinux 等安全组件

### 性能优化
1. **virtio 驱动**: 使用 virtio 网络和磁盘接口
2. **内存配置**: 根据实际需求合理分配内存
3. **磁盘格式**: 使用 qcow2 格式支持快照和压缩

### 维护管理
1. **版本控制**: 对配置文件进行版本管理
2. **自动化测试**: 建立镜像验证流程
3. **文档更新**: 及时更新配置和流程文档

---

**文档版本**: v1.0  
**创建时间**: 2025-07-30  
**维护人员**: zhaoj296  
**适用范围**: 统信 UOS 服务器版镜像制作
