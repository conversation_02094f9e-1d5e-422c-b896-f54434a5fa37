# Percona XtraBackup 8.0 安装与测试文档

## 环境信息
- **操作系统**: 麒麟系统 (ky10) ARM64 架构
- **MySQL版本**: 8.0.37
- **XtraBackup版本**: 8.0.35-33.1.el8

## 安装步骤

### 1. 解决 procps-ng 包冲突

#### 卸载系统高版本包
```bash
# 卸载麒麟系统自带的高版本 procps-ng
sudo rpm -e procps-ng-3.3.16-19.p01.ky10.aarch64 --nodeps
sudo rpm -e procps-ng-devel-3.3.16-19.p01.ky10.aarch64 --nodeps
```

#### 安装兼容版本
```bash
# 安装与 XtraBackup 兼容的版本
sudo rpm -ivh procps-ng-3.3.15-14.el8.aarch64.rpm
sudo rpm -ivh procps-ng-devel-3.3.15-14.el8.aarch64.rpm

# 验证安装
rpm -qa | grep procps-ng
ps --version
```

### 2. 安装 Perl 依赖
```bash
# 安装 Perl MySQL 驱动
sudo yum install perl-DBD-MySQL

# 验证安装
perl -MDBD::mysql -e "print 'DBD::mysql is installed\n'"
```

### 3. 安装 XtraBackup
```bash
# 安装 XtraBackup
sudo rpm -ivh percona-xtrabackup-80-8.0.35-33.1.el8.aarch64.rpm

# 验证安装
xtrabackup --version
```

## 测试步骤

### 1. 基础连接测试
```bash
# 测试连接到 MySQL
xtrabackup --backup --target-dir=/tmp/test_backup
```

### 2. 全量备份测试
```bash
# 创建备份目录
mkdir -p /tmp/test_backup

# 执行全量备份
xtrabackup --backup --target-dir=/tmp/test_backup

# 检查备份文件
ls -la /tmp/test_backup/
cat /tmp/test_backup/xtrabackup_info
cat /tmp/test_backup/xtrabackup_checkpoints
```

### 3. 备份准备
```bash
# 准备备份文件
xtrabackup --prepare --target-dir=/tmp/test_backup

# 验证准备状态
cat /tmp/test_backup/xtrabackup_checkpoints
# backup_type 应该从 "full-backuped" 变为 "full-prepared"
```

### 4. 增量备份测试
```bash
# 创建增量备份目录
mkdir -p /tmp/test_incremental

# 执行增量备份
xtrabackup --backup --target-dir=/tmp/test_incremental --incremental-basedir=/tmp/test_backup

# 准备增量备份
xtrabackup --prepare --apply-log-only --target-dir=/tmp/test_backup
xtrabackup --prepare --apply-log-only --target-dir=/tmp/test_backup --incremental-dir=/tmp/test_incremental
xtrabackup --prepare --target-dir=/tmp/test_backup
```

## 恢复测试

### 1. 停止 MySQL 服务
```bash
# 停止 MySQL 服务
sudo systemctl stop mysql
```

### 2. 备份现有数据目录
```bash
# 备份当前数据目录
sudo mv /var/lib/mysql /var/lib/mysql.backup.$(date +%Y%m%d_%H%M%S)

# 创建新的数据目录
sudo mkdir -p /var/lib/mysql
sudo chown mysql:mysql /var/lib/mysql
```

### 3. 执行恢复
```bash
# 恢复数据到 MySQL 数据目录
sudo xtrabackup --copy-back --target-dir=/tmp/test_backup

# 修复权限
sudo chown -R mysql:mysql /var/lib/mysql
sudo chmod -R 750 /var/lib/mysql
```

### 4. 启动服务并验证
```bash
# 启动 MySQL 服务
sudo systemctl start mysql

# 验证恢复结果
mysql -u root -p -e "SHOW DATABASES;"
```

### 5. 清理测试文件
```bash
# 清理测试备份文件
rm -rf /tmp/test_backup /tmp/test_incremental
```

## 测试结果验证

### 成功标志
1. **连接成功**:
   ```
   version_check Connected to MySQL server
   Connecting to MySQL server host: localhost
   Using server version 8.0.37
   ```

2. **备份成功**: 
   - 备份目录包含数据文件和日志文件
   - `xtrabackup_info` 文件包含备份元信息
   - `xtrabackup_checkpoints` 显示备份状态

3. **恢复成功**:
   ```sql
   mysql> SHOW DATABASES;
   +--------------------+
   | Database           |
   +--------------------+
   | information_schema |
   | mysql              |
   | performance_schema |
   | sys                |
   | test               |
   +--------------------+
   ```

---

## 安装过程中遇到的问题与解决方案

### 问题1: procps-ng 包版本冲突
**错误信息**:
```
procps-ng-3.3.15-14.el8 is needed by percona-xtrabackup-80-8.0.35-33.1.el8.aarch64
```

**原因分析**:
- XtraBackup 要求 `procps-ng-3.3.15-14.el8` (CentOS/RHEL版本)
- 系统已安装 `procps-ng-3.3.16-19.p01.ky10.aarch64` (麒麟系统更高版本)
- 版本号格式不同导致 RPM 认为依赖不满足

**解决方案**:
1. 卸载系统高版本包
2. 安装兼容版本包
3. 这样可以确保完全兼容，避免潜在问题

### 问题2: Perl 模块缺失
**错误信息**:
```
perl(DBD::mysql) is needed by percona-xtrabackup-80-8.0.35-33.1.el8.aarch64
```

**原因分析**:
XtraBackup 需要 Perl 的 MySQL 数据库驱动模块来执行某些操作。

**解决方案**:
通过系统包管理器安装 `perl-DBD-MySQL` 包。

### 问题3: 参数顺序错误
**错误信息**:
```
--defaults-file must be specified first on the command line
```

**原因分析**:
`--defaults-file` 是特殊参数，必须作为命令行的第一个参数出现。

**解决方案**:
将 `--defaults-file` 放在命令行最前面。

### 问题4: 不支持的参数
**错误信息**:
```
unknown option '--dry-run'
```

**原因分析**:
XtraBackup 不支持 `--dry-run` 参数，只能执行实际备份。

## 技术原理说明

### procps-ng 包的重要性
- **功能**: 提供基本的进程管理工具（ps, top, kill, free 等）
- **风险**: 卸载会导致系统无法使用基本命令
- **策略**: 先卸载高版本，立即安装兼容版本，确保系统功能不中断

### --prepare 步骤原理
**作用**: 对备份文件执行事务日志回放，确保数据一致性

**过程**:
1. **前滚（Redo）**: 应用 redo log 中已提交的事务
2. **回滚（Undo）**: 撤销 undo log 中未提交的事务

**必要性**: 
备份时数据库仍在运行，可能存在：
- 已提交但未写入数据文件的事务
- 未提交但已写入数据文件的事务
- prepare 步骤修复这种不一致状态

**类比**: 相当于 MySQL 异常关机后重启时的崩溃恢复过程

### 恢复过程原理
**为什么必须停止数据库**:
1. **文件锁定**: MySQL 运行时锁定数据文件
2. **数据一致性**: 内存缓存与磁盘文件需要保持一致
3. **进程占用**: MySQL 进程持有文件句柄

### test 数据库说明
- `test` 数据库是 MySQL 默认安装的测试数据库
- 生产环境建议删除以提高安全性
- 开发环境可保留用于测试目的

## 生产环境建议

1. **版本兼容性**: 确保 XtraBackup 版本与 MySQL 版本兼容
2. **依赖管理**: 使用兼容的依赖包版本，避免系统包冲突
3. **备份策略**: 定期全量备份 + 增量备份
4. **恢复测试**: 定期验证备份文件的可恢复性
5. **权限管理**: 创建专用的备份用户，限制权限
6. **监控告警**: 监控备份任务执行状态和备份文件完整性

---
**文档版本**: v1.1  
**更新时间**: 2025-07-28  
**维护人员**: zhaoj296
