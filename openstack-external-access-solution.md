# OpenStack双引擎架构外部访问解决方案

## 网络架构分析

### Pod网络分布特征
根据您提供的Pod信息，发现了两个不同的网段：

**100.124.x.x 网段（Pod网络）：**
- API服务类Pod：neutron-server, nova-api-osapi, nova-placement-api等
- 管理类Pod：nova-scheduler, openstack-client等
- 特点：这些是标准的Kubernetes Pod网络IP

**172.25.21.x 网段（Host网络）：**
- Agent类Pod：neutron-ovs-agent, neutron-metadata-agent等
- 计算节点Pod：nova-compute等
- 特点：这些Pod使用hostNetwork模式，直接使用物理机IP

### 网络模式说明
```yaml
# Host网络模式的Pod配置示例
apiVersion: v1
kind: Pod
spec:
  hostNetwork: true  # 使用宿主机网络
  dnsPolicy: ClusterFirstWithHostNet
```

## 方案一：Kubernetes Service暴露 + 专用测试凭证

### 1. 暴露OpenStack服务
```yaml
# keystone-external-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: keystone-external
  namespace: openstack
spec:
  type: NodePort
  ports:
  - port: 5000
    targetPort: 5000
    nodePort: 30500
  selector:
    app: keystone-api

---
# trove-external-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: trove-external
  namespace: openstack
spec:
  type: NodePort
  ports:
  - port: 8779
    targetPort: 8779
    nodePort: 30779
  selector:
    app: trove-api

---
# neutron-external-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: neutron-external
  namespace: openstack
spec:
  type: NodePort
  ports:
  - port: 9696
    targetPort: 9696
    nodePort: 30696
  selector:
    app: neutron-server
```

### 2. VM中的OpenStack客户端配置
```bash
# /etc/openstack/clouds.yaml
clouds:
  regression-test:
    auth:
      auth_url: http://K8S_NODE_IP:30500/v3
      username: regression-test-user
      password: your-password
      project_name: regression-test
      domain_name: default
    region_name: RegionOne
    interface: public
    identity_api_version: 3
    volume_api_version: 3
    compute_api_version: 2.1
    database_api_version: 1.0
    network_api_version: 2.0
```

### 3. 测试脚本示例
```bash
#!/bin/bash
# regression-test.sh

export OS_CLOUD=regression-test

# 测试连通性
echo "Testing OpenStack connectivity..."
openstack token issue

# 测试Trove功能
echo "Testing Trove database operations..."
openstack database instance create test-mysql mysql-5.7 2 --size 1
openstack database instance list
openstack database instance delete test-mysql

# 测试Neutron功能
echo "Testing Neutron network operations..."
openstack network create test-network
openstack network list
openstack network delete test-network
```

## 方案二：kubectl代理 + API转发

### 1. 在测试VM中安装kubectl
```bash
# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/

# 配置kubeconfig（只读权限）
mkdir -p ~/.kube
# 从管理节点复制kubeconfig（需要创建专用的只读ServiceAccount）
```

### 2. 创建专用ServiceAccount
```yaml
# regression-test-rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: regression-test
  namespace: openstack

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: regression-test-role
rules:
- apiGroups: [""]
  resources: ["services", "pods"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods/portforward"]
  verbs: ["create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: regression-test-binding
subjects:
- kind: ServiceAccount
  name: regression-test
  namespace: openstack
roleRef:
  kind: ClusterRole
  name: regression-test-role
  apiGroup: rbac.authorization.k8s.io
```

### 3. 动态端口转发脚本
```bash
#!/bin/bash
# setup-openstack-proxy.sh

# 获取服务Pod
KEYSTONE_POD=$(kubectl get pods -n openstack -l app=keystone-api -o jsonpath='{.items[0].metadata.name}')
TROVE_POD=$(kubectl get pods -n openstack -l app=trove-api -o jsonpath='{.items[0].metadata.name}')
NEUTRON_POD=$(kubectl get pods -n openstack -l app=neutron-server -o jsonpath='{.items[0].metadata.name}')

# 启动端口转发
kubectl port-forward -n openstack $KEYSTONE_POD 5000:5000 &
kubectl port-forward -n openstack $TROVE_POD 8779:8779 &
kubectl port-forward -n openstack $NEUTRON_POD 9696:9696 &

echo "OpenStack services are now accessible on localhost"
echo "Keystone: http://localhost:5000"
echo "Trove: http://localhost:8779"
echo "Neutron: http://localhost:9696"
```

## 方案三：Sidecar容器模式

### 1. 创建测试专用Pod
```yaml
# regression-test-pod.yaml
apiVersion: v1
kind: Pod
metadata:
  name: regression-test-runner
  namespace: openstack
spec:
  serviceAccountName: regression-test
  containers:
  - name: test-runner
    image: your-registry/openstack-client:latest
    command: ["/bin/bash", "-c", "sleep infinity"]
    env:
    - name: OS_AUTH_URL
      value: "http://keystone-api.openstack.svc.cluster.local:5000/v3"
    - name: OS_USERNAME
      value: "regression-test-user"
    - name: OS_PASSWORD
      valueFrom:
        secretKeyRef:
          name: regression-test-creds
          key: password
    volumeMounts:
    - name: test-scripts
      mountPath: /opt/tests
  volumes:
  - name: test-scripts
    configMap:
      name: regression-test-scripts
```

### 2. 执行测试
```bash
# 在管理节点执行
kubectl exec -it regression-test-runner -n openstack -- /opt/tests/run-regression.sh

# 或者通过CI/CD触发
kubectl create job regression-test-job --from=pod/regression-test-runner -n openstack
```
