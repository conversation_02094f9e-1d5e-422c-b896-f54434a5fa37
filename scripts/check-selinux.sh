#!/bin/bash
# SELinux状态检查脚本

echo "=== SELinux 状态检查 ==="
echo

# 1. 基本状态检查
echo "1. SELinux 基本状态："
if command -v sestatus >/dev/null 2>&1; then
    sestatus
else
    echo "   sestatus 命令不可用"
fi
echo

# 2. 当前模式
echo "2. 当前SELinux模式："
if command -v getenforce >/dev/null 2>&1; then
    current_mode=$(getenforce)
    echo "   当前模式: $current_mode"
    
    case $current_mode in
        "Enforcing")
            echo "   状态: ✅ SELinux已启用并强制执行策略"
            ;;
        "Permissive")
            echo "   状态: ⚠️  SELinux已启用但仅记录违规（不阻止）"
            ;;
        "Disabled")
            echo "   状态: ❌ SELinux已禁用"
            ;;
        *)
            echo "   状态: ❓ 未知状态"
            ;;
    esac
else
    echo "   getenforce 命令不可用"
fi
echo

# 3. 配置文件检查
echo "3. 配置文件设置："
if [ -f /etc/selinux/config ]; then
    config_mode=$(grep "^SELINUX=" /etc/selinux/config | cut -d= -f2)
    echo "   配置文件模式: $config_mode"
    echo "   配置文件位置: /etc/selinux/config"
    
    # 显示配置文件内容
    echo "   配置文件内容:"
    grep -E "^SELINUX|^SELINUXTYPE" /etc/selinux/config | sed 's/^/     /'
else
    echo "   ❌ 配置文件 /etc/selinux/config 不存在"
fi
echo

# 4. 内核参数检查
echo "4. 内核启动参数："
cmdline_selinux=$(cat /proc/cmdline | grep -o 'selinux=[^ ]*' || echo "未设置")
echo "   启动参数: $cmdline_selinux"
echo

# 5. SELinux文件系统检查
echo "5. SELinux文件系统："
if [ -d /sys/fs/selinux ] && [ "$(ls -A /sys/fs/selinux 2>/dev/null)" ]; then
    echo "   ✅ SELinux文件系统已挂载: /sys/fs/selinux"
    echo "   文件数量: $(ls /sys/fs/selinux | wc -l)"
else
    echo "   ❌ SELinux文件系统未挂载或为空"
fi
echo

# 6. 总结建议
echo "=== 总结和建议 ==="
if command -v getenforce >/dev/null 2>&1; then
    current_mode=$(getenforce)
    case $current_mode in
        "Disabled")
            echo "🔒 SELinux当前已禁用"
            echo "   如需启用SELinux："
            echo "   1. 编辑 /etc/selinux/config"
            echo "   2. 设置 SELINUX=enforcing 或 SELINUX=permissive"
            echo "   3. 重启系统: sudo reboot"
            ;;
        "Enforcing")
            echo "🔒 SELinux当前已启用并强制执行"
            echo "   系统安全性较高，如遇到权限问题，检查SELinux日志"
            ;;
        "Permissive")
            echo "🔒 SELinux当前处于宽松模式"
            echo "   正在记录违规但不阻止，适合调试和测试"
            ;;
    esac
else
    echo "⚠️  无法确定SELinux状态，可能系统不支持SELinux"
fi
