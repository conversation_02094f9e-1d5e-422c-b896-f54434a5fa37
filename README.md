# 工具集合

这个项目包含多个实用工具：

## 1. macOS 系统清理脚本

专为 macOS Ventura 13.6.4 设计的系统清理脚本，可以安全地清理各种临时文件、缓存文件，帮助释放磁盘空间。

## 2. VSCode Augment 自动账号切换工具

自动管理和切换VSCode Augment插件账号的工具，支持邮箱池管理和自动切换。

### 🚀 快速开始

```bash
# 1. 运行安装脚本
./setup_augment_switcher.sh

# 2. 编辑配置文件，添加你的邮箱账号
nano email_pool.json

# 3. 添加账号
./augment_<NAME_EMAIL> your_password --notes "主账号"

# 4. 测试切换
./augment_switch switch --restart

# 5. 启动自动切换守护进程
./augment_daemon
```

### 📋 主要功能

- **智能账号切换**: 基于使用频率和时间自动选择最优账号
- **邮箱池管理**: 支持多个邮箱账号的统一管理
- **自动化运行**: 守护进程自动监控和切换账号
- **使用统计**: 详细的账号使用统计和历史记录
- **跨平台支持**: macOS、Windows、Linux
- **安全性**: 本地存储，不上传敏感信息

### 📖 详细文档

查看 [AUGMENT_SWITCHER_README.md](AUGMENT_SWITCHER_README.md) 获取完整的使用说明和配置指南。

## 功能特性

### 🧹 清理内容
- **用户缓存**: 应用程序缓存文件
- **系统临时文件**: /tmp 目录下的临时文件
- **日志文件**: 用户和系统日志
- **浏览器缓存**: Safari、Chrome、Firefox 等
- **开发工具缓存**: npm、yarn、gradle、maven 等
- **字体缓存**: 系统字体缓存
- **Spotlight 缓存**: 搜索索引缓存
- **DNS 缓存**: 网络 DNS 缓存
- **.DS_Store 文件**: 系统隐藏文件

### 🛡️ 安全特性
- 彩色输出，清晰显示清理进度
- 安全删除，避免误删重要文件
- 错误处理，遇到问题不会中断整个清理过程
- 显示清理前文件夹大小
- 支持普通用户和管理员两种模式

## 使用方法

### 1. 赋予执行权限
```bash
chmod +x macos_cleanup.sh
```

### 2. 普通用户模式运行
```bash
./macos_cleanup.sh
```
这将清理用户级别的缓存和临时文件。

### 3. 管理员模式运行（推荐）
```bash
sudo ./macos_cleanup.sh
```
这将进行更深度的系统级清理，包括系统缓存和日志。

## 清理详情

### 用户级清理
- `~/Library/Caches/*` - 用户应用缓存
- `~/Library/Logs/*` - 用户日志文件
- `~/.Trash/*` - 回收站内容
- `~/Downloads/*.tmp` - 下载临时文件
- 各种开发工具缓存

### 系统级清理（需要 sudo）
- `/Library/Caches/*` - 系统应用缓存
- `/System/Library/Caches/*` - 系统库缓存
- `/var/log/*` - 系统日志
- `/private/var/log/*` - 私有系统日志

### 特殊清理
- DNS 缓存刷新
- Spotlight 索引重建
- 字体缓存清理
- 浏览器特定缓存

## 注意事项

⚠️ **重要提醒**:
1. 清理前建议关闭所有正在运行的应用程序
2. 某些应用可能需要重新登录或重新配置
3. 开发环境的缓存清理后可能需要重新下载依赖
4. 建议在清理后重启系统

## 预期效果

根据系统使用情况，通常可以释放：
- **轻度使用**: 1-3 GB
- **中度使用**: 3-8 GB  
- **重度使用**: 8-20 GB 或更多

## 定期维护建议

建议每月运行一次此脚本进行系统维护，保持系统性能和磁盘空间。

## 故障排除

如果遇到权限问题：
```bash
# 检查脚本权限
ls -la macos_cleanup.sh

# 重新赋予权限
chmod +x macos_cleanup.sh
```

如果某些文件无法删除，这是正常现象，脚本会跳过并继续清理其他文件。

## 兼容性

- ✅ macOS Ventura 13.6.4
- ✅ macOS Monterey 12.x
- ✅ macOS Big Sur 11.x
- ⚠️ 其他版本未测试，但应该兼容

## 自定义

你可以根据需要修改脚本中的清理路径，添加或移除特定的清理目标。
