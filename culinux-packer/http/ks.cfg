# CULinux 4.0 Kickstart 配置文件
# 基于 OpenEuler 22.03

# 系统语言和键盘 - 强制设置
lang zh_CN.UTF-8
keyboard --vckeymap=us --xlayouts='us'
# 跳过X Window配置
skipx

# 时区设置
timezone Asia/Shanghai --isUtc

# 安装模式 - 从光盘安装，完全自动化
cdrom
text
skipx
reboot
# 强制自动化安装
autopart --type=lvm
zerombr
clearpart --all --initlabel

# 网络配置
network --bootproto=dhcp --device=eth0 --onboot=on --ipv6=auto --activate
network --hostname=culinux4-base

# 安全配置
firewall --enabled --ssh
selinux --disabled
authselect --enableshadow --passalgo=sha512

# 用户配置
rootpw --plaintext Culinux4@2025

# 磁盘分区 - 简化自动分区
# clearpart --all --initlabel  # 已在上面定义
# 使用自动分区而不是手动分区，减少交互
# part /boot --fstype="xfs" --size=500
# part swap --fstype="swap" --size=2048  
# part / --fstype="xfs" --size=1 --grow

# 引导加载器 - IDE接口使用hda而不是vda，启用串口控制台
bootloader --location=mbr --boot-drive=hda --append="net.ifnames=0 biosdevname=0 console=tty0 console=ttyS0,115200"

# 软件包选择
%packages --ignoremissing --excludedocs
@core
@base
# 云计算相关
cloud-init
cloud-utils-growpart
# 网络工具
openssh-server
openssh-clients
net-tools
wget
curl
# 系统工具
vim
htop
tree
# 系统管理和监控工具
iotop
nethogs
tcpdump
strace
lsof
# 开发工具
gcc
make
git
%end

# 安装前脚本
%pre
# 记录安装开始时间
echo "CULinux 4.0 installation started at $(date)" > /tmp/install.log
%end

# 安装后脚本
%post --log=/root/kickstart-post.log

# 设置root密码
echo 'root:Culinux4@2025' | chpasswd

# SSH配置优化
sed -i 's/^#PermitRootLogin.*/PermitRootLogin yes/' /etc/ssh/sshd_config
sed -i 's/^PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config
sed -i 's/^#PubkeyAuthentication.*/PubkeyAuthentication yes/' /etc/ssh/sshd_config

# 网络配置
systemctl enable NetworkManager
# 配置eth0自动连接
nmcli connection modify "System eth0" connection.autoconnect yes 2>/dev/null || true

# 启用必要服务
systemctl enable sshd
systemctl enable cloud-init
systemctl enable cloud-config
systemctl enable cloud-final
systemctl enable cloud-init-local

# 启用串口控制台登录（标准方法）
<NAME_EMAIL>

# 配置安全终端 - 允许root通过串口登录
grep -q ttyS0 /etc/securetty || echo "ttyS0" >> /etc/securetty

# 配置GRUB添加串口控制台支持
# 检查GRUB配置文件是否存在，并添加串口控制台参数
if [ -f /etc/default/grub ]; then
    # 检查是否已经包含console=ttyS0,115200
    if ! grep -q "console=ttyS0,115200" /etc/default/grub; then
        # 在GRUB_CMDLINE_LINUX行末尾添加串口控制台参数
        # 处理可能存在的多种格式
        sed -i '/^GRUB_CMDLINE_LINUX=/ {
            s/"$/ console=tty0 console=ttyS0,115200"/
            s/\([^"]\)$/"&"/
        }' /etc/default/grub
        
        # 重新生成GRUB配置
        grub2-mkconfig -o /boot/grub2/grub.cfg
        
        echo "已添加串口控制台到GRUB配置" >> /root/kickstart-post.log
        echo "修改后的GRUB_CMDLINE_LINUX:" >> /root/kickstart-post.log
        grep "^GRUB_CMDLINE_LINUX=" /etc/default/grub >> /root/kickstart-post.log
    else
        echo "GRUB配置中已存在串口控制台参数" >> /root/kickstart-post.log
    fi
else
    echo "警告: /etc/default/grub 文件不存在" >> /root/kickstart-post.log
fi

# 创建串口控制台检查脚本（基于标准方法）
cat > /usr/local/bin/check-serial-console.sh << 'EOF'
#!/bin/bash
# 串口控制台状态检查和修复脚本 - 基于标准方法

echo "=== 串口控制台状态检查 ==="

# 1. 检查安全终端配置
echo "🔍 检查 /etc/securetty 配置："
if grep -q ttyS0 /etc/securetty; then
    echo "✅ ttyS0 已在 /etc/securetty 中"
else
    echo "❌ ttyS0 不在 /etc/securetty 中，正在添加..."
    echo "ttyS0" >> /etc/securetty
fi

# 2. 检查GRUB配置
echo "🔍 检查 GRUB 串口控制台配置："
if [ -f /etc/default/grub ]; then
    if grep -q "console=ttyS0,115200" /etc/default/grub; then
        echo "✅ GRUB配置中已包含串口控制台参数"
    else
        echo "❌ GRUB配置中缺少串口控制台参数，正在添加..."
        sed -i 's/GRUB_CMDLINE_LINUX="\(.*\)"/GRUB_CMDLINE_LINUX="\1 console=ttyS0,115200"/' /etc/default/grub
        grub2-mkconfig -o /boot/grub2/grub.cfg
        echo "⚠️  需要重启系统使GRUB配置生效"
    fi
else
    echo "❌ /etc/default/grub 文件不存在"
fi

# 3. 检查和启动服务
echo "🔍 检查 serial-getty@ttyS0 服务："
systemctl is-enabled <EMAIL> >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ serial-getty@ttyS0 服务已启用"
else
    echo "❌ serial-getty@ttyS0 服务未启用，正在启用..."
    <NAME_EMAIL>
fi

systemctl is-active <EMAIL> >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ serial-getty@ttyS0 服务正在运行"
else
    echo "❌ serial-getty@ttyS0 服务未运行，正在启动..."
    <NAME_EMAIL>
    sleep 2
    <NAME_EMAIL> --no-pager
fi

# 4. 检查设备
echo "🔍 检查串口设备："
if [ -c /dev/ttyS0 ]; then
    echo "✅ /dev/ttyS0 设备存在"
    ls -la /dev/ttyS0
else
    echo "❌ /dev/ttyS0 设备不存在"
fi

echo "=== 检查完成 ==="
echo "💡 如果串口控制台仍不工作，请："
echo "   1. 确认虚拟机启动参数包含 -serial stdio"
echo "   2. 使用 'virsh console <vm-name>' 连接"
echo "   3. 检查内核消息: dmesg | grep -i serial"
EOF

chmod +x /usr/local/bin/check-serial-console.sh

# 可选：设置开机自动检查串口控制台（调试用）
cat > /etc/systemd/system/check-serial-console.service << 'EOF'
[Unit]
Description=Check and fix serial console configuration
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/check-serial-console.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 注释掉自动启用，用户可根据需要手动启用
# systemctl enable check-serial-console.service

# 导入CULinux GPG密钥并配置YUM源
curl -o /etc/pki/rpm-gpg/RPM-GPG-KEY-culinux http://************:9006/repository/culinux/RPM-GPG-KEY-culinux
rpm --import /etc/pki/rpm-gpg/RPM-GPG-KEY-culinux

# 下载并使用CULinux官方repo文件
curl -o /etc/yum.repos.d/culinux-harbin.repo http://************:9006/repository/culinux/4.0/culinux-harbin.repo

# 备份原有的OpenEuler源
if [ -f /etc/yum.repos.d/openEuler.repo ]; then
    mv /etc/yum.repos.d/openEuler.repo /etc/yum.repos.d/openEuler.repo.bak
fi

# 清理YUM缓存并更新
yum clean all
yum makecache

# Cloud-init配置 - 适配现有云平台
mkdir -p /etc/cloud/cloud.cfg.d/

# 数据源配置 - 与现有云平台保持一致
cat > /etc/cloud/cloud.cfg.d/90_datasource.cfg << 'EOF'
# 数据源配置 - 使用ConfigDrive模式（与现有云平台一致）
datasource_list: [ConfigDrive, None]
EOF

# 用户和认证配置 - 与现有云平台保持一致  
cat > /etc/cloud/cloud.cfg.d/20_users.cfg << 'EOF'
# 用户配置 - 与现有云平台一致
users:
 - default
disable_root: 0
ssh_pwauth: 1
mount_default_fields: [~, ~, 'auto', 'defaults,nofail', '0', '2']
resize_rootfs_tmp: /dev

# hosts文件管理 - 使用模板模式，允许保留现有条目
manage_etc_hosts: template
preserve_hostname: false
EOF

# 创建hosts模板文件，保留wocloud proxy配置
cat > /etc/cloud/templates/hosts.tmpl << 'EOF'
# /etc/cloud/templates/hosts.tmpl
# 这个模板由cloud-init使用，支持变量替换

127.0.0.1 localhost
::1 localhost ip6-localhost ip6-loopback

# 实例hostname配置（由cloud-init自动填充）
{{fqdn}} {{hostname}}

# wocloud proxy configuration (保留现有配置)
# 这部分会被保留，不会被cloud-init覆盖
{{#proxy_entries}}
{{ip}} {{hostname}}
{{/proxy_entries}}

# 可以在这里添加其他固定的hosts条目
EOF

# SSH密钥管理配置
cat > /etc/cloud/cloud.cfg.d/70_ssh.cfg << 'EOF'
# SSH主机密钥配置
ssh_deletekeys: true
ssh_genkeytypes: ['rsa', 'ecdsa', 'ed25519']
EOF

# 设置系统时区
timedatectl set-timezone Asia/Shanghai

# 预配置wocloud proxy hosts条目（如果需要）
cat >> /etc/hosts << 'EOF'

# wocloud proxy configuration
# 这些条目会被保留，除非cloud-init使用完全覆盖模式
# proxy.wocloud.com 10.x.x.x
# api.wocloud.com 10.x.x.y
EOF

# 创建必要的目录
mkdir -p /var/log/cloud-init

# 记录安装完成信息
echo "CULinux 4.0 installation completed at $(date)" >> /root/kickstart-post.log
echo "System ready for cloud deployment" >> /root/kickstart-post.log

%end
