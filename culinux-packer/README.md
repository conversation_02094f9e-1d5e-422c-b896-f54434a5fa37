# CULinux 4.0 Packer 自动化镜像制作项目

## 项目概述

本项目用于通过 Packer 自动化制作 CULinux 4.0 操作系统的 qcow2 镜像文件。CULinux 4.0 基于 OpenEuler 22.03，适用于云平台部署。

## 目录结构

```
culinux-packer/
├── culinux.pkr.hcl          # Packer 主配置文件
├── http/
│   └── ks.cfg               # Kickstart 自动安装配置
├── scripts/
│   └── cleanup.sh           # 镜像清理脚本
└── README.md                # 项目说明文档
```

## 使用前准备

### 1. 环境要求

- Linux 操作系统（推荐 CentOS/RHEL/OpenEuler）
- QEMU/KVM 虚拟化环境
- Packer 1.8+ 版本
- 网络访问 CULinux YUM 源（SSH端口59599）

### 2. 部署到服务器

**使用SFTP上传项目文件到服务器:**
```bash
# 服务器操作
mkdir -p /opt/culinux-packer
cd /opt/culinux-packer

# 设置脚本执行权限
chmod +x scripts/*.sh
```

### 3. ISO 镜像配置

项目配置中 ISO 镜像路径为：
```
/opt/packer-base-image/os/CULinux-4.0-kr720.x86_64.iso
```

请确保服务器上该路径下存在 CULinux 4.0 ISO 文件。

### 4. YUM 源配置

项目自动使用官方提供的 repo 文件：
- **Repo 文件**: `http://************:9006/repository/culinux/4.0/culinux-harbin.repo`
- **GPG 密钥**: `http://************:9006/repository/culinux/RPM-GPG-KEY-culinux`

## 构建流程

### 1. 验证配置

```bash
# 验证 Packer 配置文件语法
packer validate culinux.pkr.hcl
```

### 2. 开始构建

```bash
# 基本构建
packer build culinux.pkr.hcl

# 调试模式（推荐首次使用）
packer build -debug -on-error=ask culinux.pkr.hcl
```

### 3. 监控进度

构建过程中可以通过 VNC 监控安装进度：
```bash
# 连接 VNC（端口 5909）
vncviewer localhost:5909
```

## 配置说明

### 系统配置

- **Root 密码**: `culinux@2025`
- **SSH 端口**: `59599`（适配服务器环境）
- **时区**: `Asia/Shanghai`
- **分区方案**: LVM（/boot + LVM 卷组）
- **文件系统**: XFS
- **SELinux**: 禁用
- **防火墙**: 启用（允许 SSH）

### YUM 源配置

自动下载并使用官方 repo 配置：
- **官方 Repo**: `http://************:9006/repository/culinux/4.0/culinux-harbin.repo`
- **GPG 密钥**: `http://************:9006/repository/culinux/RPM-GPG-KEY-culinux`

### 预装软件

- Cloud-init 云初始化
- OpenSSH 服务器
- 基础开发工具
- Python3 环境
- 系统监控工具

## 输出结果

构建成功后将生成：
```
output-culinux/
└── culinux-4.0-base.qcow2    # 可用于云平台部署的镜像文件
```

## 验证测试

### 本地测试

```bash
# 使用 QEMU 启动镜像测试
/usr/libexec/qemu-kvm \
  -m 4096 \
  -smp 4 \
  -drive file=output-culinux/culinux-4.0-base.qcow2,format=qcow2 \
  -net nic,model=virtio \
  -net user,hostfwd=tcp::2222-:22 \
  -enable-kvm \
  -display vnc=:10

# SSH 连接测试
ssh -p 2222 root@localhost
```

### 功能验证

```bash
# 登录后验证系统信息
cat /etc/os-release
yum repolist enabled
systemctl status cloud-init
```

## 故障排除

### 常见问题

1. **YUM 源连接失败**
   - 检查网络连通性
   - 验证 GPG 密钥下载

2. **Kickstart 安装失败**
   - 使用 VNC 查看安装日志
   - 检查 Kickstart 语法

3. **SSH 连接超时**
   - 确认防火墙配置
   - 检查 SSH 服务状态

### 调试技巧

- 使用 `-debug` 参数启用详细日志
- 通过 VNC 实时监控安装过程
- 检查 `/tmp/anaconda.log` 安装日志

## 部署使用

### OpenStack 集成

```bash
# 上传镜像到 Glance
openstack image create \
  --container-format bare \
  --disk-format qcow2 \
  --file output-culinux/culinux-4.0-base.qcow2 \
  --property os_distro=culinux \
  --property os_version=4.0 \
  "CULinux-4.0-Base"
```

### libvirt 集成

```bash
# 导入到 libvirt
virt-install \
  --name culinux-test \
  --memory 4096 \
  --vcpus 4 \
  --disk path=/path/to/culinux-4.0-base.qcow2,format=qcow2 \
  --import \
  --os-variant rhel8.0 \
  --network default
```

## 维护更新

- 定期更新 ISO 镜像版本
- 更新系统安全补丁
- 优化配置参数
- 测试验证功能

---

**项目维护**: zhaoj296  
**创建时间**: 2025-07-30  
**适用版本**: CULinux 4.0 (基于 OpenEuler 22.03)
