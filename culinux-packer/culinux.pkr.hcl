packer {
  required_plugins {
    qemu = {
      version = ">= 1.0.9"
      source  = "github.com/hashicorp/qemu"
    }
  }
}

source "qemu" "culinux" {
  // 基本配置
  iso_url           = "/opt/packer-base-image/os/CULinux-4.0-kr720.x86_64.iso"
  iso_checksum      = "b8582275b7060cbf5961b92e72d2a8d5d1db8a53b1a52ed4a9fa2016d218cf33"  // 实际使用时应添加ISO校验和
  output_directory  = "output-culinux"
  vm_name           = "culinux-4.0-base.qcow2"
  headless          = true
  accelerator       = "kvm"

  // 虚拟机硬件配置 - 基于 virt-install 成功配置
  cpus              = 4
  memory            = 4096
  disk_size         = "50G"    // 设置50G虚拟容量，qcow2实际占用4-8G
  format            = "qcow2"
  disk_interface    = "ide"    // 改用IDE接口，兼容性更好
  net_device        = "e1000"  // 改用e1000网卡，兼容性更好

  // QEMU配置
  qemu_binary       = "/usr/libexec/qemu-kvm"
  
  // VNC配置
  vnc_bind_address  = "0.0.0.0"
  vnc_port_min      = 5909
  vnc_port_max      = 5909
  
  // HTTP服务配置
  http_directory    = "http"
  http_port_min     = 8000
  http_port_max     = 8100

  // 启动配置 - 基于官方 CULinux 4.0 kr720 配置
  boot_wait         = "10s"
  boot_command = [
    "<tab>",
    " inst.ks=http://{{ .HTTPIP }}:{{ .HTTPPort }}/ks.cfg",
    " inst.text inst.selinux=0 fpi_to_tail=off",
    "<enter><wait>"
  ]

  // SSH连接配置
  ssh_username      = "root"
  ssh_password      = "Culinux4@2025"
  ssh_timeout       = "30m"
  ssh_port          = 59599

  // 关机配置
  shutdown_command  = "echo 'Culinux4@2025' | sudo -S shutdown -P now"
  shutdown_timeout  = "15m"
  
  // QEMU运行参数 - 模拟 virt-install 成功配置
  qemuargs = [
    ["-m", "4096M"],
    ["-smp", "4"],
    ["-machine", "pc-i440fx-6.2"],
    ["-cpu", "host"],
    ["-serial", "stdio"]
  ]
}

build {
  sources = ["source.qemu.culinux"]
  
  // 执行清理脚本
  provisioner "shell" {
    scripts = [
      "scripts/cleanup.sh"
    ]
  }
}
