#!/bin/bash
# CULinux 4.0 镜像清理脚本

set -e

echo "开始清理 CULinux 4.0 镜像..."

# 清理包管理器缓存
echo "清理YUM缓存..."
yum clean all
rm -rf /var/cache/yum/*
rm -rf /var/cache/dnf/*

# 清理RPM数据库临时文件
echo "清理RPM临时文件..."
rm -rf /var/lib/rpm/__db*

# 清理临时文件和日志
echo "清理临时文件..."
rm -rf /tmp/*
rm -rf /var/tmp/*
rm -rf /root/.cache/*

# 清理系统日志
echo "清理系统日志..."
journalctl --flush
journalctl --rotate
journalctl --vacuum-time=1s

# 清理传统日志文件
find /var/log -type f -name "*.log" -exec truncate -s 0 {} \;
find /var/log -type f -name "*.log.*" -delete
truncate -s 0 /var/log/messages
truncate -s 0 /var/log/lastlog
truncate -s 0 /var/log/wtmp
truncate -s 0 /var/log/btmp

# 清理SSH相关文件
echo "清理SSH配置..."
rm -f /etc/ssh/ssh_host_*
rm -rf /root/.ssh/
rm -f /home/<USER>/.ssh/authorized_keys 2>/dev/null || true

# 清理网络配置
echo "清理网络配置..."
rm -f /etc/udev/rules.d/70-persistent-net.rules
rm -f /etc/udev/rules.d/75-persistent-net-generator.rules

# 清理机器ID
echo "清理机器标识..."
> /etc/machine-id
> /var/lib/dbus/machine-id

# 清理cloud-init状态
echo "清理cloud-init状态..."
rm -rf /var/lib/cloud/*
rm -rf /var/log/cloud-init*

# 清理命令历史
echo "清理命令历史..."
history -c
> /root/.bash_history
> /root/.viminfo

# 清理Python缓存
echo "清理Python缓存..."
find / -name "*.pyc" -delete 2>/dev/null || true
find / -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 清理内核模块缓存
echo "清理内核缓存..."
rm -rf /lib/modules/*/kernel/drivers/gpu/drm/nouveau/
depmod -a

# 零化剩余空间（可选，会增加镜像制作时间）
echo "优化磁盘空间..."
dd if=/dev/zero of=/EMPTY bs=1M 2>/dev/null || true
rm -f /EMPTY

# 同步文件系统
sync

echo "CULinux 4.0 镜像清理完成"
