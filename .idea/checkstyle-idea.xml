<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CheckStyle-IDEA">
    <option name="configuration">
      <map>
        <entry key="checkstyle-version" value="8.8" />
        <entry key="copy-libs" value="false" />
        <entry key="location-0" value="BUNDLED:(bundled):Sun Checks" />
        <entry key="location-1" value="BUNDLED:(bundled):Google Checks" />
        <entry key="location-2" value="LOCAL_FILE:/Users/<USER>/IdeaProjects/sharding-sphere/sharding-sphere/src/resources/checkstyle.xml:sharding-sphere-checks" />
        <entry key="location-3" value="LOCAL_FILE:$PRJ_DIR$//Users/<USER>/IdeaProjects/sharding-shpere/src/resources/sharding_checks.xml:sharding-style" />
        <entry key="scan-before-checkin" value="false" />
        <entry key="scanscope" value="Everything" />
        <entry key="suppress-errors" value="false" />
      </map>
    </option>
  </component>
</project>