#!/bin/bash
#filename: Enhance_dts_sec.sh
#author: zhuzy56
#date: 2025-06-04
source /root/openrc
logfile=/tmp/dts_sec.log
echo "正在获取dts安全组规则，请等待..."
dts_seg_id=$(openstack security group list | grep "dts_8190_4646" | head -n1 | awk '{print$2}')
if [[ -z $dts_seg_id ]]; then
    echo "WARNNING: ✅  没有dts安全组, 不需要进行加固"
    exit 1
else
    true > $logfile
fi

# 检查 jq 是否安装
if ! command -v jq &>/dev/null; then
  echo "当前环境无jq,正在安装jq..."
  cp jq /usr/sbin/jq
  chmod 755 /usr/sbin/jq
fi

echo "首先验证dts接口，期望接口正常返回"
# sunshine_vip_address=$(cat /etc/haproxy/haproxy.cfg|grep bind|grep 8190| grep -v '#'|awk -F'[ :]+' '{print$3}'| head -n 1)
sunshine_vip_address="************"
echo "查询所有的节点数 curl -X GET "http://${sunshine_vip_address}:8190/v2/nodes" -H "accept: application/json""
curl -X GET --connect-timeout 2 "http://${sunshine_vip_address}:8190/v2/nodes" -H "accept: application/json"
echo "查询所有的任务 curl -X GET "http://${sunshine_vip_address}:8190/v2/jobs" -H "accept: application/json""
curl -X GET --connect-timeout 2 "http://${sunshine_vip_address}:8190/v2/jobs" -H "accept: application/json"

#第一次展示
rules=$(openstack security group rule list --ingress $dts_seg_id)
echo "当前的ingress安全组" "$rules"
echo "正在重设安全组..."

# 获取已有的所有规则
RULE_IDS=$(openstack security group rule list --ingress $dts_seg_id|awk '{print$2}'| grep -v 'ID')
wait

### 先判断当前的安全组
declare -A RULE_MAP
data=$(openstack security group rule list --ingress $dts_seg_id -f json)
# 使用 jq 遍历 JSON 数组，提取字段并填充 Map
while IFS= read -r line; do
    if [[ -n "$line" ]]; then
        key_part="${line%%#*}"  # 获取 # 前的部分（作为 Key）
        value_part="${line##*#}" # 获取 # 后的部分（作为 Value）
        RULE_MAP["$key_part"]=$value_part
    fi
done < <(echo "$data" | jq -r '.[] | "\(.["IP Range"])_\(.["IP Protocol"])_\(.["Port Range"])#\(.["ID"])"')

## ***********/26 网段的tcp 和udp 单独处理，防止造成dts内部服务闪断
## 如果已经存在这两个安全组，不能删除，其他的都可以删除，可能造成dts接口短暂不可用
rule1="RULE1"
dts_tcp_rule="***********/26_tcp_"
if [ -n "${RULE_MAP[$dts_tcp_rule]+isset}" ]; then
    echo "✅ $dts_tcp_rule：已经存在"
    rule1=${RULE_MAP[$dts_tcp_rule]}
else
    openstack security group rule create  --proto tcp --ingress  --remote-ip ***********/26 $dts_seg_id
    echo "❌ $dts_tcp_rule 不存在，先添加"
fi

rule2="RULE2"
dts_udp_rule="***********/26_udp_"
if [ -n "${RULE_MAP[$dts_udp_rule]+isset}" ]; then
    echo "✅ $dts_udp_rule：已经存在"
    rule2=${RULE_MAP[$dts_udp_rule]}
else
    openstack security group rule create  --proto udp --ingress  --remote-ip ***********/26 $dts_seg_id
    echo "❌ $dts_udp_rule 不存在，先添加"
fi

# 删除原来的规则
for RULE_ID in $RULE_IDS; do
    if [[ "$RULE_ID" == "$rule1" || "$RULE_ID" == "$rule2" ]]; then
        echo "跳过安全组：$RULE_ID"
        continue  # 跳过当前循环，进入下一个迭代
    fi
    echo "删除安全组规则" $RULE_ID
    openstack security group rule delete $RULE_ID >>$logfile 2>&1
done
wait

#重写安全组规则
# 原来的DTS安全组
# neutron security-group-create dts_8190_4646
# openstack security group rule create  --proto tcp --dst-port 4646:4646 --ingress  --remote-ip 0.0.0.0/0 dts_8190_4646
# openstack security group rule create  --proto tcp --dst-port 8190:8190 --ingress  --remote-ip 0.0.0.0/0 dts_8190_4646
# openstack security group rule create  --proto tcp --dst-port 59599:59599 --ingress  --remote-ip 0.0.0.0/0 dts_8190_4646
# openstack security group rule create  --proto icmp --ingress  --remote-ip 0.0.0.0/0  dts_8190_4646
# openstack security group rule create  --proto tcp --ingress  --remote-ip ***********/16 dts_8190_4646
# openstack security group rule create  --proto udp --ingress  --remote-ip ***********/16 dts_8190_4646
#优化后的安全组
openstack security group rule create --proto tcp --dst-port 8190:8190 --ingress  --remote-ip 10.0.0.0/8 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto tcp --dst-port 8190:8190 --ingress  --remote-ip **********/8 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto tcp --dst-port 8190:8190 --ingress  --remote-ip ***********/16 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto tcp --dst-port 59599:59599 --ingress --remote-ip 10.0.0.0/8 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto tcp --dst-port 59599:59599 --ingress --remote-ip **********/8 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto tcp --dst-port 59599:59599 --ingress --remote-ip ***********/16 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto icmp --ingress --remote-ip 10.0.0.0/8 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto icmp --ingress --remote-ip **********/8 $dts_seg_id >>$logfile 2>&1
openstack security group rule create --proto icmp --ingress --remote-ip ***********/16 $dts_seg_id >>$logfile 2>&1
## 这两个规则上面单独判断是否存在添加
# openstack security group rule create  --proto tcp --ingress  --remote-ip ***********/26 $dts_seg_id >>$logfile 2>&1
# openstack security group rule create  --proto udp --ingress  --remote-ip ***********/26 $dts_seg_id >>$logfile 2>&1
# 不操作egress规则
# openstack security group rule create --egress --remote-ip 0.0.0.0/0 $dts_seg_id >>$logfile 2>&1 
wait


#展示成果
echo "安全组重设结果如下:"
openstack security group rule list --ingress $dts_seg_id

RULE_MAP=()
data=$(openstack security group rule list --ingress $dts_seg_id -f json)
# 使用 jq 遍历 JSON 数组，提取字段并填充 Map
while IFS= read -r line; do
    if [[ -n "$line" ]]; then
        RULE_MAP["$line"]=1
        echo $line
    fi
done < <(echo "$data" | jq -r '.[] | "\(.["IP Range"])_\(.["IP Protocol"])_\(.["Port Range"])"')

echo "验证安全组规则是否全部设置"
rules=("10.0.0.0/8_tcp_8190:8190"
    "**********/8_tcp_8190:8190"
    "***********/16_tcp_8190:8190"
    "10.0.0.0/8_tcp_59599:59599"
    "**********/8_tcp_59599:59599"
    "***********/16_tcp_59599:59599"
    "10.0.0.0/8_icmp_"
    "**********/8_icmp_"
    "***********/16_icmp_"
    "***********/26_tcp_"
    "***********/26_udp_")

for item in "${rules[@]}"; do
    # if [[ -v RULE_MAP[$item] ]]; then
    if [ -n "${RULE_MAP[$item]+isset}" ]; then
        echo "✅ 安全组设置成功：$item"
    else
        echo "❌ 安全组设置失败：$item"
    fi
done


### 增加旧安全组删除成功的检查
delete_ruls=("0.0.0.0/0_tcp_8190:8190"
    "0.0.0.0/0_tcp_4646:4646"
    "0.0.0.0/0_tcp_59599:59599")
for item in "${delete_ruls[@]}"; do
    if [ -n "${RULE_MAP[$item]+isset}" ]; then
        echo "❌ 旧安全组删除失败：$item"
    else
        echo "✅ 旧安全组删除成功：$item"
    fi
done


echo "再次验证dts接口，期望接口正常返回"
sunshine_vip_address="************"
echo "查询所有的节点数 curl -X GET "http://${sunshine_vip_address}:8190/v2/nodes" -H "accept: application/json""
curl -X GET --connect-timeout 2 "http://${sunshine_vip_address}:8190/v2/nodes" -H "accept: application/json"
echo "查询所有的任务 curl -X GET "http://${sunshine_vip_address}:8190/v2/jobs" -H "accept: application/json""
curl -X GET --connect-timeout 2 "http://${sunshine_vip_address}:8190/v2/jobs" -H "accept: application/json"