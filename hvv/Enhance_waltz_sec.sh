#!/bin/bash
#filename: Enhance_waltz_sec.sh
#author: zhuzy56
#date: 2025-06-04
source /root/openrc
logfile=/tmp/sec.log
echo "正在获取waltz安全组规则，请等待..."
waltz_seg_id=$(openstack security group list | grep -w "waltz-all" | head -n1 | awk '{print$2}')
if [[ -z $waltz_seg_id ]]; then
    echo "WARNNING: ✅ 没有waltz安全组, 不需要进行加固"
    exit 1
else
    true > $logfile
fi


# 检查 jq 是否安装
if ! command -v jq &>/dev/null; then
  echo "当前环境无jq,正在安装jq..."
  cp jq /usr/sbin/jq
  chmod 755 /usr/sbin/jq
fi

#第一次展示
openstack security group rule list --ingress $waltz_seg_id
echo "正在重设安全组..."
# 获取所有规则
RULE_IDS=$(openstack security group rule list --ingress $waltz_seg_id|awk '{print$2}'| grep -v 'ID')
wait

# 删除规则
for RULE_ID in $RULE_IDS; do
    echo "删除安全组规则" $RULE_ID
    openstack security group rule delete $RULE_ID >>$logfile 2>&1
done
wait


#先重写安全组规则
# 原来的waltz安全组
# neutron security-group-create waltz-all
# openstack security group rule create --proto tcp --dst-port 3306:59599 --ingress --remote-ip 0.0.0.0/0 waltz-all
# neutron security-group-rule-create --protocol icmp --direction ingress --remote-ip 0.0.0.0/0 waltz-all

openstack security group rule create --proto tcp --dst-port 59599:59599 --ingress --remote-ip 10.0.0.0/8 $waltz_seg_id >>$logfile 2>&1
openstack security group rule create --proto tcp --dst-port 59599:59599 --ingress --remote-ip **********/8 $waltz_seg_id >>$logfile 2>&1
openstack security group rule create --proto tcp --dst-port 59599:59599 --ingress --remote-ip ***********/16 $waltz_seg_id >>$logfile 2>&1
openstack security group rule create --proto icmp --ingress --remote-ip 10.0.0.0/8 $waltz_seg_id >>$logfile 2>&1
openstack security group rule create --proto icmp --ingress --remote-ip **********/8 $waltz_seg_id >>$logfile 2>&1
openstack security group rule create --proto icmp --ingress --remote-ip ***********/16 $waltz_seg_id >>$logfile 2>&1
# openstack security group rule create --egress --remote-ip 0.0.0.0/0 $waltz_seg_id >>$logfile 2>&1 #不操作egress规则
wait


#展示成果
echo "安全组设置结果如下:"
openstack security group rule list --ingress  $waltz_seg_id

echo "验证安全组规则设置结果"
declare -A RULE_MAP
data=$(openstack security group rule list --ingress $waltz_seg_id -f json)
# 使用 jq 遍历 JSON 数组，提取字段并填充 Map
while IFS= read -r line; do
    if [[ -n "$line" ]]; then
        RULE_MAP["$line"]=1
        echo $line
    fi
done < <(echo "$data" | jq -r '.[] | "\(.["IP Range"])_\(.["IP Protocol"])_\(.["Port Range"])"')

echo "验证安全组规则是否全部设置"
rules=("10.0.0.0/8_tcp_59599:59599"
    "**********/8_tcp_59599:59599"
    "***********/16_tcp_59599:59599"
    "10.0.0.0/8_icmp_"
    "**********/8_icmp_"
    "***********/16_icmp_")

for item in "${rules[@]}"; do
    # if [[ -v RULE_MAP[$item] ]]; then
    if [ -n "${RULE_MAP[$item]+isset}" ]; then
        echo "✅ 安全组设置成功：$item"
    else
        echo "❌ 安全组设置失败：$item"
    fi
done

### 增加旧安全组删除成功的检查
delete_ruls=("0.0.0.0/0_tcp_3306:59599"
    "0.0.0.0/0_icmp_")
for item in "${delete_ruls[@]}"; do
    # if [[ -v RULE_MAP[$item] ]]; then
    if [ -n "${RULE_MAP[$item]+isset}" ]; then
        echo "❌ 旧安全组删除失败：$item"
    else
        echo "✅ 旧安全组删除成功：$item"
    fi
done