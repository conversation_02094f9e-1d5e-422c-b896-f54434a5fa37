#!/bin/bash

sh wxeditsecret.ex.sh trove-etc trove.conf
sh wxeditsecret.ex.sh trove-etc trove-guestagent.conf

kubectl get deploy -n openstack | grep trove | awk '{print $1}' | xargs -i kubectl rollout restart deploy {} -n openstack

echo
echo "等待Trove重启..."
sleep 90

echo "检查双引擎环境启动信息"
kubectl get pods -n openstack | grep trove
trovePod=`kubectl get pods -n openstack | grep trove-api | head -n 1 | awk '{print $1}'`
echo "检查双引擎环境trove配置"
kubectl exec $trovePod -n openstack -- grep -e access_key -e secret_key /etc/trove/trove.conf
echo "检查双引擎环境trove-guestagent配置"
kubectl exec $trovePod -n openstack -- grep -e access_key -e secret_key /etc/trove/trove-guestagent.conf
