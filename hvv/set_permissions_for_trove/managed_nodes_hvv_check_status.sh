#!/bin/bash

ak1=$(grep '^access_key' /etc/trove/trove.conf | cut -d '=' -f 2 | tr -d ' ')
sk1=$(grep '^secret_key' /etc/trove/trove.conf | cut -d '=' -f 2 | tr -d ' ')
ak2=$(grep '^access_key' s3.readwrite.conf | cut -d '=' -f 2 | tr -d ' ')
sk2=$(grep '^secret_key' s3.readwrite.conf | cut -d '=' -f 2 | tr -d ' ')
ak3=$(grep '^access_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
sk3=$(grep '^secret_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
ak4=$(grep '^access_key' /etc/trove/trove-guestagent.conf | cut -d '=' -f 2 | tr -d ' ')
sk4=$(grep '^secret_key' /etc/trove/trove-guestagent.conf | cut -d '=' -f 2 | tr -d ' ')

#rgcc0001
echo "检查单引擎环境rgcc0001的启动信息"
sudo systemctl status openstack-trove-api | grep Active
sudo systemctl status openstack-trove-taskmanager | grep Active
sudo systemctl status openstack-trove-conductor | grep Active
echo "检查单引擎环境rgcc0001的trove配置"
grep -e access_key -e secret_key /etc/trove/trove.conf
echo "检查单引擎环境rgcc0001的trove-guestagent配置"
grep -e access_key -e secret_key /etc/trove/trove-guestagent.conf

echo
#rgcc0002
echo "检查单引擎环境rgcc0002的启动信息"
ssh rgcc0002 "sudo systemctl status openstack-trove-api | grep Active"
ssh rgcc0002 "sudo systemctl status openstack-trove-taskmanager | grep Active"
ssh rgcc0002 "sudo systemctl status openstack-trove-conductor | grep Active"
echo "检查单引擎环境rgcc0002的trove配置"
ssh rgcc0002 "grep -e access_key -e secret_key /etc/trove/trove.conf"
echo "检查单引擎环境rgcc0002的trove-guestagent配置"
ssh rgcc0002 "grep -e access_key -e secret_key /etc/trove/trove-guestagent.conf"

echo
#rgcc0003
echo "检查单引擎环境rgcc0003的启动信息"
ssh rgcc0003 "sudo systemctl status openstack-trove-api | grep Active"
ssh rgcc0003 "sudo systemctl status openstack-trove-taskmanager | grep Active"
ssh rgcc0003 "sudo systemctl status openstack-trove-conductor | grep Active"
echo "检查单引擎环境rgcc0003的trove配置"
ssh rgcc0003 "grep -e access_key -e secret_key /etc/trove/trove.conf"
echo "检查单引擎环境rgcc0003的trove-guestagent配置"
ssh rgcc0003 "grep -e access_key -e secret_key /etc/trove/trove-guestagent.conf"
