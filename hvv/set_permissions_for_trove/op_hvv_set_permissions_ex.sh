#!/bin/bash
source ~/openrc

ak2=$(grep '^access_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
sk2=$(grep '^secret_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
nme=$(grep '^name' instance.conf | cut -d '=' -f 2 | tr -d ' ')

# 双引擎环境尝试进入MariaDB
if kubectl exec mariadb-server-control-0 -n openstack -- \
    mysql --defaults-file=/etc/mysql/admin_user.cnf trove -e "SELECT 1" &>/dev/null; then
  #echo "升级双引擎环境配置信息"
  #sh two_engines_hvv_set_permissions.sh

  echo
  echo "使用双引擎环境查询数据"
  instance_ids=$(kubectl exec mariadb-server-control-0 -n openstack -- \
      mysql --defaults-file=/etc/mysql/admin_user.cnf trove -se \
      "SELECT id FROM instances WHERE name LIKE '%$nme%' AND deleted = 0")

else
  #echo "升级单引擎环境配置信息"
  #ssh rgcc0002 echo
  #if [ $? -eq 0 ]; then
  #    echo "可以免密登录rgcc0002和rgcc0003"
  #    sh managed_nodes_hvv_set_permissions.sh
  #else
  #    echo "不能免密登录rgcc0002和rgcc0003，请在rgcc0002和rgcc0003单独执行managed_other_hvv_set_permissions.sh"
  #    sh managed_other_hvv_set_permissions.sh
  #fi

  echo
  echo "使用单引擎环境查询数据"
  pass=`grep pymysql /etc/trove/trove.conf | grep -Eo 'trove:[^@]*' | awk -F ':' '{print $2}'`
  instance_ids=$(mysql -utrove -p$pass -s -e "SELECT id FROM trove.instances WHERE name LIKE '%$nme%' AND deleted = 0")
fi

sh instance_op_hvv_set_permissions.sh "$instance_ids"
