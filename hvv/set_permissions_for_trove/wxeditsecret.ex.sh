#!/usr/bin/bash
if [ ! -n "$1" ] || [ ! -n "$2" ]; then
    echo "usage: wxeditsecret secret_name key_name"
    exit 1
fi

ns=openstack
tmpfile=$(mktemp)
kubectl get secret -n $ns $1 >/dev/null
if [ ! $? -eq 0 ]; then
    exit 1
fi

tpl="{{index .data \"$2\"}}"
kubectl get secret -n $ns $1 --template="${tpl}" | base64 -d >$tmpfile
if [ $? -gt 0 ]; then
    echo "ERROR!! Cannot find $2 in secret $1"
    exit 1
fi

res=$(md5sum $tmpfile | awk '{print $1}')

#vim $tmpfile
ak1=$(grep '^access_key' $tmpfile | cut -d '=' -f 2 | tr -d ' ')
sk1=$(grep '^secret_key' $tmpfile | cut -d '=' -f 2 | tr -d ' ')
ak2=$(grep '^access_key' s3.readwrite.conf | cut -d '=' -f 2 | tr -d ' ')
sk2=$(grep '^secret_key' s3.readwrite.conf | cut -d '=' -f 2 | tr -d ' ')
ak3=$(grep '^access_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
sk3=$(grep '^secret_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
if [ "$2" == "trove.conf" ]; then
    sed -i "s/$ak1/$ak2/g" $tmpfile
    sed -i "s/$sk1/$sk2/g" $tmpfile
elif [ "$2" == "trove-guestagent.conf" ]; then
    sed -i "s/$ak1/$ak3/g" $tmpfile
    sed -i "s/$sk1/$sk3/g" $tmpfile
fi

newres=$(md5sum $tmpfile | awk '{print $1}')
if [ "$res" == "$newres" ]; then
    echo "WARN!! no change, just ignore"
    exit 1
fi

tpl="{\"data\":{\"$2\":\"{}\"}}"
cat $tmpfile | base64 -w 0 | xargs -i kubectl patch secret -n $ns $1 -p "${tpl}"
