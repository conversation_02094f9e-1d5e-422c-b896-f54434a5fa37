#!/bin/bash

ak1=$(grep '^access_key' /etc/trove/conf.d/trove-guestagent.conf | cut -d '=' -f 2 | tr -d ' ')
sk1=$(grep '^secret_key' /etc/trove/conf.d/trove-guestagent.conf | cut -d '=' -f 2 | tr -d ' ')
ak2=$(grep '^access_key' /var/log/trove/s3.conf | cut -d '=' -f 2 | tr -d ' ')
sk2=$(grep '^secret_key' /var/log/trove/s3.conf | cut -d '=' -f 2 | tr -d ' ')
sudo su - trove -c "chmod -077 -R /etc/trove/conf.d/*"
sudo su - trove -c "chmod -077 -R /var/log/trove/*"
sudo cp -r /usr/lib/python2.7/site-packages/trove /usr/lib/python2.7/site-packages/trove.bak
sudo su - root -c "chmod -077 -R /usr/lib/python2.7/site-packages/trove/*"
sudo chown -R trove:trove /usr/lib/python2.7/site-packages/trove
sudo sed -i "s/$ak1/$ak2/g" /etc/trove/conf.d/trove-guestagent.conf
sudo sed -i "s/$sk1/$sk2/g" /etc/trove/conf.d/trove-guestagent.conf
sudo su - root -c "chmod -077 -R /etc/telegraf/*"
sudo systemctl restart telegraf
sudo systemctl restart openstack-trove-guestagent
