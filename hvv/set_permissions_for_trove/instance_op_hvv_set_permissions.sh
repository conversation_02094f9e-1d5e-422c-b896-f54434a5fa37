#!/bin/bash
source ~/openrc

instance_ids=$1

set_s3writeonly_base64=$(base64 s3.writeonly.conf)
set_permissions_base64=$(base64 hvv_set_permissions.sh)

# 遍历每个 instance_id 并执行命令
step=20
instance_array=($(echo $instance_ids | awk '{for(i=1;i<=NF;i++) print $i}'))
for ((i=0; i<${#instance_array[@]}; i+=step)); do
  for ((j=i; j<i+step; j+=1)); do
  (
    if [ $j -lt ${#instance_array[@]} ]; then
      instance_id=${instance_array[$j]}
      echo "开始处理实例:$instance_id"
      trove --timeout 5 agent-exec-cmd $instance_id "sudo echo \"$set_s3writeonly_base64\" | base64 -d >/var/log/trove/s3.conf" 2>/dev/null
      trove --timeout 5 agent-exec-cmd $instance_id "sudo echo \"$set_permissions_base64\" | base64 -d >/var/log/trove/hvv_set_permissions.sh" 2>/dev/null
      trove --timeout 5 agent-exec-cmd $instance_id 'sudo sh /var/log/trove/hvv_set_permissions.sh &>/var/log/trove/hvv_set_permissions.log &' 2>/dev/null
    fi
  ) &
  done
  wait
  sleep 5
  echo "Coninute ..."
done

echo
echo "等待Agent重启..."
sleep 30

# 遍历每个 instance_id 并执行命令
for ((i=0; i<${#instance_array[@]}; i+=step)); do
  for ((j=i; j<i+step; j+=1)); do
  (
    if [ $j -lt ${#instance_array[@]} ]; then
      instance_id=${instance_array[$j]}
      akey=`trove --timeout 5 agent-exec-cmd $instance_id 'sudo grep access_key /etc/trove/conf.d/trove-guestagent.conf' 2>/dev/null | grep key`
      echo "开始查询实例:$instance_id:aksk, $akey";
      agnt=`trove --timeout 5 agent-exec-cmd $instance_id 'sudo systemctl status openstack-trove-guestagent | grep Active' 2>/dev/null | grep Active`
      echo "开始查询实例:$instance_id:guestagent, $agnt";
      tele=`trove --timeout 5 agent-exec-cmd $instance_id 'sudo systemctl status telegraf | grep Active' 2>/dev/null | grep Active`
      echo "开始查询实例:$instance_id:telegraf, $tele";
    fi
  ) &
  done
  wait
  sleep 5
  echo "Coninute ..."
done

echo
echo "Finished."
