#!/bin/bash

ak1=$(grep '^access_key' /etc/trove/trove.conf | cut -d '=' -f 2 | tr -d ' ')
sk1=$(grep '^secret_key' /etc/trove/trove.conf | cut -d '=' -f 2 | tr -d ' ')
ak2=$(grep '^access_key' s3.readwrite.conf | cut -d '=' -f 2 | tr -d ' ')
sk2=$(grep '^secret_key' s3.readwrite.conf | cut -d '=' -f 2 | tr -d ' ')
ak3=$(grep '^access_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
sk3=$(grep '^secret_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
ak4=$(grep '^access_key' /etc/trove/trove-guestagent.conf | cut -d '=' -f 2 | tr -d ' ')
sk4=$(grep '^secret_key' /etc/trove/trove-guestagent.conf | cut -d '=' -f 2 | tr -d ' ')

#rgcc0002, rgcc0003
echo "更新单引擎环境rgcc的ak/sk..."
sudo sed -i "s/$ak1/$ak2/g" /etc/trove/trove.conf
sudo sed -i "s/$sk1/$sk2/g" /etc/trove/trove.conf
sudo sed -i "s/$ak4/$ak3/g" /etc/trove/trove-guestagent.conf
sudo sed -i "s/$sk4/$sk3/g" /etc/trove/trove-guestagent.conf
sudo systemctl restart openstack-trove-api
sudo systemctl restart openstack-trove-taskmanager
sudo systemctl restart openstack-trove-conductor

echo "等待单引擎环境rgcc的Trove重启..."
sleep 30

echo "检查单引擎环境rgcc的启动信息"
sudo systemctl status openstack-trove-api | grep Active
sudo systemctl status openstack-trove-taskmanager | grep Active
sudo systemctl status openstack-trove-conductor | grep Active
echo "检查单引擎环境rgcc的trove配置"
grep -e access_key -e secret_key /etc/trove/trove.conf
echo "检查单引擎环境rgcc的trove-guestagent配置"
grep -e access_key -e secret_key /etc/trove/trove-guestagent.conf
