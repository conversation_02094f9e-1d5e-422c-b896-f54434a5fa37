#!/bin/bash

ak2=$(grep '^access_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')
sk2=$(grep '^secret_key' s3.writeonly.conf | cut -d '=' -f 2 | tr -d ' ')

grep "开始处理实例" hvv.log | awk -F ':' '{print $2}' >instance.all
num_all=`wc -l instance.all`
echo "更新实例个数：$num_all"

grep ", access_key = $ak2" hvv.log | awk -F ':' '{print $2}' | awk -F ',' '{print $1}' >instance.success
num_succ=`wc -l instance.success`
echo "成功实例个数：$num_succ"

cat instance.success instance.all | sort | uniq -u >instance.failed
num_fail=`wc -l instance.failed`
echo "失败实例个数：$num_fail"

grep ":guestagent" hvv.log | grep -v running | awk -F ',' '{print $1}' | awk -F ':' '{print $2}' >instance.guestagent
num_guestagent=`wc -l instance.guestagent`
echo "guestagent失败个数：$num_guestagent"

grep ":telegraf" hvv.log | grep -v running | awk -F ',' '{print $1}' | awk -F ':' '{print $2}' >instance.telegraf
num_telegraf=`wc -l instance.telegraf`
echo "telegraf失败个数：$num_telegraf"

echo "备份中断统计："
if kubectl exec mariadb-server-control-0 -n openstack -- \
	mysql --defaults-file=/etc/mysql/admin_user.cnf trove -e "SELECT 1" &>/dev/null; then
    kubectl exec mariadb-server-control-0 -n openstack -- \
	  mysql --defaults-file=/etc/mysql/admin_user.cnf trove -se \
	  "select id,size,instance_id,state from backups where deleted = 0 and state in ('NEW','BUILDING') and created > '2025-06-16 20:00:00'"
else
    pass=`grep pymysql /etc/trove/trove.conf | grep -Eo 'trove:[^@]*' | awk -F ':' '{print $2}'`
    mysql -utrove -p$pass -s -e "select id,size,instance_id,state from trove.backups where deleted = 0 and state in ('NEW', 'BUILDING') and created > '2025-06-16 20:00:00'"
fi
