文件说明:
(1)managed_nodes_hvv_set_permissions.sh，更换单引擎环境管理节点ak/sk。
(2)managed_others_hvv_set_permissions.sh，如果从rgcc0001无法免密登录rgcc0002和rgcc0003，则在这2个管理节点单独执行此文件。
(3)two_engines_hvv_set_permissions.sh，更换双引擎环境管理节点ak/sk。
(4)wxeditsecret.ex.sh，kubectl执行操作的脚本。
(5)hvv_set_permissions.sh，对虚拟机内部文件授权以及更换ak/sk。
(6)op_hvv_set_permissions.sh，正式执行的脚本。
(7)s3.readwrite.conf，读写ak/sk的配置文件。*** 运行前，需要手动赋值读写access_key和secret_key，用来配置在管理服务中 ***
(8)s3.writeonly.conf，只写ak/sk的配置文件。*** 运行前，需要手动赋值只写access_key和secret_key，用来配置在数据库实例中 ***
(9)instance.conf，匹配实例名称的配置文件。*** 运行前，需要手动修改模糊匹配的实例名称。如果匹配全量实例，则将name设置为空即可 ***

执行示例：
sh op_hvv_set_permissions.sh

注意，单引擎环境，其余管理节点无法免密时，拷贝全量文件，只单独执行：
sh managed_others_hvv_set_permissions.sh

另外注意，因为挂起或者其它故障的数据库实例，无法执行脚本，需要人工介入解决
