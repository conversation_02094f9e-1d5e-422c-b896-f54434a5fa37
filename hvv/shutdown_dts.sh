#!/bin/bash

source ~/openrc

server_name="dts-master-1"  # 替换为目标服务器名称

# 执行命令并过滤结果（--name 参数支持精确匹配）
result=$(openstack server list --name "$server_name" --format value -c Name)

# 判断结果是否非空
if [[ -n "$result" ]]; then
  echo "✅ DTS存在，需要关闭DTS服务"
else
  echo "✅ DTS不存在，不需要关闭DTS服务"
  exit 1
fi

openstack server stop dts-master-1 dts-master-2 dts-master-3 dts-worker-1 dts-worker-2 dts-worker-3 dts-worker-4 dts-worker-5 dts-worker-6
# openstack server start dts-master-1 dts-master-2 dts-master-3 dts-worker-1 dts-worker-2 dts-worker-3 dts-worker-4 dts-worker-5 dts-worker-6

result=$(openstack server list --name "$server_name" --format value -c Status)

if [[ "$result" == "ACTIVE" ]]; then
  echo "❌ DTS服务关闭异常"
else
  echo "✅ DTS服务成功关闭"
fi