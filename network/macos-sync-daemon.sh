#!/bin/bash
# macOS文件监控同步脚本
# 监控WSL同步过来的文件，自动上传到公司服务器
#
# 安装位置: ~/bin/macos-sync-daemon.sh
# 使用方法: macos-sync-daemon.sh {start|stop|status|sync|test}
#
# 作者: zhaoj296

set -uo pipefail

# 配置变量
WATCH_DIR="$HOME/CLionProjects/unicom/image-packer"
REMOTE_HOST="***********"
REMOTE_USER="dbgroupuser"
REMOTE_PATH="/home/<USER>/image-packer/"
LOG_FILE="$HOME/wsl-sync.log"
PID_FILE="$HOME/wsl-sync.pid"

# 日志函数
log() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$msg"
    echo "$msg" >> "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    if ! command -v fswatch >/dev/null 2>&1; then
        log "错误: fswatch 未安装，请运行: brew install fswatch"
        exit 1
    fi
    
    if ! command -v rsync >/dev/null 2>&1; then
        log "错误: rsync 未安装"
        exit 1
    fi
    
    log "依赖检查通过 ✅"
}

# 创建监控目录
setup_directories() {
    if [ ! -d "$WATCH_DIR" ]; then
        mkdir -p "$WATCH_DIR"
        log "创建监控目录: $WATCH_DIR"
    fi
    
    # 创建日志文件
    touch "$LOG_FILE"
    log "监控目录已准备就绪"
}

# 测试到公司服务器的连接
test_remote_connection() {
    log "测试到公司服务器的连接..."
    
    # 这里需要你在macOS上配置SSH密钥或者输入密码
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$REMOTE_USER@$REMOTE_HOST" "echo 'connection test'" >/dev/null 2>&1; then
        log "公司服务器连接测试成功 ✅"
        return 0
    else
        log "公司服务器连接测试失败 ❌"
        log "请确保:"
        log "1. macOS已连接公司VPN"
        log "2. SSH密钥已配置或可以密码登录"
        log "3. 服务器地址和用户名正确"
        return 1
    fi
}

# 执行同步
sync_files() {
    local change_count=$1
    log "检测到 $change_count 个文件变化，开始同步..."
    
    # 使用rsync同步文件
    if rsync -avz --delete \
        --exclude='.git/' \
        --exclude='.vscode/' \
        --exclude='node_modules/' \
        --exclude='*.log' \
        --exclude='tmp/' \
        --exclude='.DS_Store' \
        --exclude='*.backup' \
        "$WATCH_DIR/" \
        "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH" 2>>"$LOG_FILE"; then
        log "同步完成 ✅"
    else
        log "同步失败 ❌ (详细错误请查看日志)"
    fi
}

# 启动监控守护进程
start_daemon() {
    # 检查是否已经在运行
    if [ -f "$PID_FILE" ] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        log "同步守护进程已在运行 (PID: $(cat "$PID_FILE"))"
        return 1
    fi
    
    log "启动文件监控同步守护进程..."
    log "监控目录: $WATCH_DIR"
    log "目标服务器: $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH"
    log "日志文件: $LOG_FILE"
    
    # 启动fswatch监控
    fswatch -o "$WATCH_DIR" | while read num; do
        sync_files "$num"
    done &
    
    # 保存进程ID
    echo $! > "$PID_FILE"
    log "守护进程已启动 (PID: $!)"
    log "使用 '$0 stop' 停止监控"
}

# 停止守护进程
stop_daemon() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log "停止同步守护进程 (PID: $pid)..."
            kill "$pid"
            rm -f "$PID_FILE"
            log "守护进程已停止"
        else
            log "守护进程未运行"
            rm -f "$PID_FILE"
        fi
    else
        log "未找到PID文件，守护进程可能未运行"
    fi
}

# 查看状态
show_status() {
    echo "=== macOS同步守护进程状态 ==="
    echo "监控目录: $WATCH_DIR"
    echo "目标服务器: $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH"
    echo "日志文件: $LOG_FILE"
    echo ""
    
    if [ -f "$PID_FILE" ] && kill -0 "$(cat "$PID_FILE")" 2>/dev/null; then
        echo "状态: 运行中 ✅ (PID: $(cat "$PID_FILE"))"
    else
        echo "状态: 未运行 ❌"
    fi
    
    echo ""
    echo "监控目录内容:"
    if [ -d "$WATCH_DIR" ]; then
        ls -la "$WATCH_DIR" 2>/dev/null || echo "目录为空"
    else
        echo "监控目录不存在"
    fi
    
    echo ""
    echo "最近的日志 (最后10行):"
    if [ -f "$LOG_FILE" ]; then
        tail -10 "$LOG_FILE"
    else
        echo "暂无日志"
    fi
}

# 手动同步
manual_sync() {
    log "执行手动同步..."
    if [ -d "$WATCH_DIR" ] && [ "$(ls -A "$WATCH_DIR" 2>/dev/null)" ]; then
        sync_files "manual"
    else
        log "监控目录为空，无需同步"
    fi
}

# 主函数
main() {
    case "${1:-}" in
        start)
            check_dependencies
            setup_directories
            if test_remote_connection; then
                start_daemon
            else
                log "连接测试失败，请检查网络和认证配置"
                exit 1
            fi
            ;;
        stop)
            stop_daemon
            ;;
        status)
            show_status
            ;;
        sync)
            manual_sync
            ;;
        test)
            check_dependencies
            setup_directories
            test_remote_connection
            ;;
        *)
            echo "macOS文件监控同步工具"
            echo ""
            echo "用法: $0 {start|stop|status|sync|test}"
            echo ""
            echo "命令说明:"
            echo "  start  - 启动监控守护进程"
            echo "  stop   - 停止监控守护进程"
            echo "  status - 查看运行状态"
            echo "  sync   - 手动执行一次同步"
            echo "  test   - 测试依赖和连接"
            echo ""
            echo "工作流程:"
            echo "1. WSL通过SFTP同步文件到macOS的 ~/wsl-sync/"
            echo "2. macOS监控该目录，自动同步到公司服务器"
            echo "3. 实现WSL → macOS → 公司服务器的无缝同步"
            echo ""
            echo "安装说明:"
            echo "1. 将此脚本复制到 ~/bin/macos-sync-daemon.sh"
            echo "2. 添加执行权限: chmod +x ~/bin/macos-sync-daemon.sh"
            echo "3. 确保 ~/bin 在PATH中: echo 'export PATH=\"\$HOME/bin:\$PATH\"' >> ~/.zshrc"
            exit 1
            ;;
    esac
}

main "$@"
