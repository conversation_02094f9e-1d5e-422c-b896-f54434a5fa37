#!/bin/bash
# SFTP配置切换脚本 - 支持方案1的双向同步
# 作者: zhaoj296

set -uo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VSCODE_DIR="$SCRIPT_DIR"  # 脚本现在就在.vscode目录中

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 备份当前配置
backup_current_config() {
    if [ -f "$VSCODE_DIR/sftp.json" ]; then
        local current_name=$(grep '"name"' "$VSCODE_DIR/sftp.json" | head -1 | sed 's/.*"name": *"\([^"]*\)".*/\1/')
        case "$current_name" in
            *"macOS Bridge"*)
                mv "$VSCODE_DIR/sftp.json" "$VSCODE_DIR/sftp-macos.json.disabled"
                log "已备份当前macOS配置"
                ;;
            *"poc08"*)
                mv "$VSCODE_DIR/sftp.json" "$VSCODE_DIR/sftp-poc08.json.disabled"
                log "已备份当前poc08配置"
                ;;
            *"guigu"*)
                mv "$VSCODE_DIR/sftp.json" "$VSCODE_DIR/sftp-guigu.json.disabled"
                log "已备份当前guigu配置"
                ;;
            *)
                mv "$VSCODE_DIR/sftp.json" "$VSCODE_DIR/sftp-unknown.json.disabled"
                log "已备份当前未知配置"
                ;;
        esac
    fi
}

# 切换到macOS桥接配置 (方案1)
switch_to_macos() {
    log "切换到 macOS Bridge 配置..."

    # 备份当前配置
    backup_current_config

    # 检查是否有备用配置
    if [ -f "$VSCODE_DIR/sftp-macos.json.disabled" ]; then
        mv "$VSCODE_DIR/sftp-macos.json.disabled" "$VSCODE_DIR/sftp.json"
        log "已激活备用的macOS配置"
    else
        # 创建新的macOS配置
        cat > "$VSCODE_DIR/sftp.json" << 'EOF'
{
    "name": "macOS Bridge",
    "host": "*************",
    "protocol": "sftp",
    "port": 22,
    "username": "zhaojun",
    "remotePath": "/Users/<USER>/wsl-sync/",
    "uploadOnSave": true,
    "downloadOnOpen": false,
    "ignore": [
        ".vscode",
        ".git",
        "**/.DS_Store",
        "**/node_modules/**",
        "**/*.log",
        "**/tmp/**",
        "**/.backup"
    ],
    "watcher": {
        "files": "**/*",
        "autoUpload": true,
        "autoDelete": true
    },
    "syncOption": {
        "delete": false,
        "skipCreate": false,
        "ignoreExisting": false,
        "update": true
    }
}
EOF
        log "已创建新的macOS配置"
    fi

    log "已切换到 macOS Bridge 配置 ✅"
    log "现在保存文件会自动同步到macOS"
    log "请确保macOS上的同步守护进程已启动:"
    log "  macOS命令: ~/bin/macos-sync-daemon.sh start"
}

# 切换到poc08直连配置
switch_to_poc08() {
    log "切换到 poc08 直连配置..."

    # 备份当前配置
    backup_current_config

    # 检查是否有备用配置
    if [ -f "$VSCODE_DIR/sftp-poc08.json.disabled" ]; then
        mv "$VSCODE_DIR/sftp-poc08.json.disabled" "$VSCODE_DIR/sftp.json"
        log "已激活备用的poc08配置"
    else
        # 创建新的poc08配置
        cat > "$VSCODE_DIR/sftp.json" << 'EOF'
{
    "name": "poc08 (Direct)",
    "host": "***********",
    "protocol": "sftp",
    "port": 22,
    "username": "dbgroupuser",
    "password": "Mob}v~@p5in_5w{\\",
    "remotePath": "/home/<USER>/image-packer/",
    "uploadOnSave": false,
    "downloadOnOpen": false,
    "ignore": [
        ".vscode",
        ".git",
        "**/.DS_Store",
        "**/node_modules/**",
        "**/*.log",
        "**/tmp/**"
    ],
    "watcher": {
        "files": "**/*",
        "autoUpload": false,
        "autoDelete": false
    },
    "syncOption": {
        "delete": false,
        "skipCreate": false,
        "ignoreExisting": false,
        "update": true
    }
}
EOF
        log "已创建新的poc08配置"
    fi

    log "已切换到 poc08 直连配置 ✅"
    log "需要手动使用 SFTP 命令同步"
}

# 切换到guigu配置
switch_to_guigu() {
    log "切换到 guigu 配置..."

    # 备份当前配置
    backup_current_config

    # 检查是否有备用配置
    if [ -f "$VSCODE_DIR/sftp-guigu.json.disabled" ]; then
        mv "$VSCODE_DIR/sftp-guigu.json.disabled" "$VSCODE_DIR/sftp.json"
        log "已激活备用的guigu配置"
    else
        # 创建新的guigu配置
        cat > "$VSCODE_DIR/sftp.json" << 'EOF'
{
    "name": "guigu-arm (************)",
    "host": "************",
    "protocol": "sftp",
    "port": 22,
    "username": "root",
    "password": "wo1trove2cloud#mysql$",
    "remotePath": "/opt/image-packer/",
    "uploadOnSave": false,
    "downloadOnOpen": false,
    "ignore": [
        ".vscode",
        ".git",
        "**/.DS_Store",
        "**/node_modules/**",
        "**/*.log",
        "**/tmp/**"
    ],
    "watcher": {
        "files": "**/*",
        "autoUpload": false,
        "autoDelete": false
    },
    "syncOption": {
        "delete": false,
        "skipCreate": false,
        "ignoreExisting": false,
        "update": true
    }
}
EOF
        log "已创建新的guigu配置"
    fi

    log "已切换到 guigu-arm 配置 ✅"
    log "需要手动使用 SFTP 命令同步"
}

# 显示当前配置
show_current() {
    if [ -f "$VSCODE_DIR/sftp.json" ]; then
        local current_name=$(grep '"name"' "$VSCODE_DIR/sftp.json" | head -1 | sed 's/.*"name": *"\([^"]*\)".*/\1/')
        local upload_on_save=$(grep '"uploadOnSave"' "$VSCODE_DIR/sftp.json" | head -1 | sed 's/.*"uploadOnSave": *\([^,]*\).*/\1/')
        log "当前配置: $current_name"
        log "自动上传: $upload_on_save"
    else
        log "未找到 sftp.json 配置文件"
    fi
}

# 恢复多配置文件
restore_multi() {
    log "恢复多配置文件..."
    cat > "$VSCODE_DIR/sftp.json" << 'EOF'
[
    {
        "name": "macOS Bridge",
        "host": "*************",
        "protocol": "sftp",
        "port": 22,
        "username": "zhaojun",
        "remotePath": "/Users/<USER>/wsl-sync/",
        "uploadOnSave": true,
        "downloadOnOpen": false,
        "ignore": [
            ".vscode",
            ".git",
            "**/.DS_Store",
            "**/node_modules/**",
            "**/*.log",
            "**/tmp/**",
            "**/.backup"
        ],
        "watcher": {
            "files": "**/*",
            "autoUpload": true,
            "autoDelete": true
        },
        "syncOption": {
            "delete": false,
            "skipCreate": false,
            "ignoreExisting": false,
            "update": true
        }
    },
    {
        "name": "poc08 (Direct)",
        "host": "***********",
        "protocol": "sftp",
        "port": 22,
        "username": "dbgroupuser",
        "password": "Mob}v~@p5in_5w{\\",
        "remotePath": "/home/<USER>/image-packer/",
        "uploadOnSave": false,
        "downloadOnOpen": false,
        "ignore": [
            ".vscode",
            ".git",
            "**/.DS_Store",
            "**/node_modules/**",
            "**/*.log",
            "**/tmp/**"
        ],
        "watcher": {
            "files": "**/*",
            "autoUpload": false,
            "autoDelete": false
        },
        "syncOption": {
            "delete": false,
            "skipCreate": false,
            "ignoreExisting": false,
            "update": true
        }
    }
]
EOF
    log "已恢复多配置文件 ✅"
}

# 检查macOS同步守护进程
check_macos_daemon() {
    log "检查macOS同步守护进程..."
    log "请确保在macOS上执行以下操作:"
    log "1. 将 macos-sync-daemon.sh 复制到 ~/bin/ 目录"
    log "2. 添加执行权限: chmod +x ~/bin/macos-sync-daemon.sh"
    log "3. 启动守护进程: ~/bin/macos-sync-daemon.sh start"
    log "4. 查看状态: ~/bin/macos-sync-daemon.sh status"
}

# 主函数
main() {
    case "${1:-}" in
        macos|m|bridge)
            switch_to_macos
            ;;
        poc08|p|direct)
            switch_to_poc08
            ;;
        guigu|g)
            switch_to_guigu
            ;;
        current|c)
            show_current
            ;;
        multi|restore)
            restore_multi
            ;;
        check-daemon|daemon)
            check_macos_daemon
            ;;
        *)
            echo "SFTP配置切换工具 - 支持方案1双向同步"
            echo ""
            echo "用法: $0 {macos|poc08|guigu|current|multi|check-daemon}"
            echo ""
            echo "配置说明:"
            echo "  macos       (m) - macOS Bridge (方案1推荐) 🌟"
            echo "  poc08       (p) - poc08 直连"
            echo "  guigu       (g) - guigu-arm 服务器"
            echo "  current     (c) - 显示当前配置"
            echo "  multi           - 恢复多配置文件"
            echo "  check-daemon    - 检查macOS守护进程配置"
            echo ""
            echo "方案1工作流程:"
            echo "1. 使用 '$0 macos' 切换到macOS桥接模式"
            echo "2. 在macOS上启动: ~/bin/macos-sync-daemon.sh start"
            echo "3. WSL编辑保存 → macOS → 公司服务器 (全自动)"
            echo ""
            echo "使用示例:"
            echo "  $0 macos        # 启用方案1 (推荐)"
            echo "  $0 poc08        # 直连模式"
            echo "  $0 current      # 查看当前配置"
            echo "  $0 check-daemon # 检查macOS守护进程"
            exit 1
            ;;
    esac
}

main "$@"

