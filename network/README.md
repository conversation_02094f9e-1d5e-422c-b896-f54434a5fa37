# 网络切换工具

macOS网络智能管理工具，专为护网期间和日常网络切换设计。

## 核心功能

### network_switch.sh ⭐
**唯一推荐使用的脚本** - 集成了所有网络管理功能，提供完整的解决方案。

## 增强版功能特性

## 🚀 核心特性

### 智能网络管理
- **智能重试机制**: 热点连接失败时自动重试，提高连接成功率
- **网络可见性检测**: 使用airport工具扫描网络，支持激活iPhone热点
- **DNS缓存管理**: 自动清除DNS缓存，解决网络切换后的连通性问题
- **静态IP集成**: 自动配置有线网络静态IP，无需手动设置
- **连接状态验证**: 检查IP地址获取和网络连通性，确保切换成功

### 护网安全功能 🛡️
- **ClashX智能管理**: 有线模式自动关闭ClashX，热点模式智能启动
- **安全状态监控**: 实时检查网络安全状态，识别潜在风险
- **流量隔离路由**: 安全模式下确保办公网流量直连，不经过代理
- **强制安全模式**: 一键进入最安全配置（关闭代理+有线直连）
- **网络连通性测试**: 自动验证DNS解析和Google访问

### 持续监控
- **后台监控模式**: 持续监控网络状态，断线自动重连
- **进程管理**: 支持启动、停止、检查监控进程
- **详细日志记录**: 统一的日志系统，支持调试模式

## 📋 网络模式详解

### 1. 热点模式 (`hotspot`)
**适用场景**: 个人网络环境，需要访问被墙网站
**工作流程**:
```
禁用有线网络 → 启用WiFi → 连接iPhone热点 → 清除DNS缓存 →
启动ClashX → 验证Google访问 → 完成切换
```

### 2. 有线网络模式 (`ethernet`)
**适用场景**: 护网期间办公环境，需要纯净网络连接
**工作流程**:
```
关闭ClashX → 禁用WiFi → 启用有线网络 → 配置静态IP →
设置DNS服务器 → 清除DNS缓存 → 验证网络连通性 → 完成切换
```

### 3. 安全模式 (`secure`)
**适用场景**: 需要同时使用办公网和个人网络
**工作流程**:
```
管理ClashX状态 → 启用有线网络 → 连接热点 → 清除DNS缓存 →
配置智能路由 → 验证双网络连接 → 完成切换
```

### 4. 监控模式 (`hotspot-monitor` / `secure-monitor`)
**适用场景**: 需要长期稳定的网络连接
**工作流程**:
```
执行对应模式切换 → 启动后台监控进程 → 定期检查网络状态 →
检测到问题时自动修复 → 持续循环监控
```

## 🚀 快速开始

### 基本网络切换
```bash
# 切换到热点模式（个人网络）
./network_switch.sh hotspot

# 切换到有线模式（办公网络，护网安全）
./network_switch.sh ethernet

# 切换到安全模式（双网络）
./network_switch.sh secure

# 查看当前网络状态
./network_switch.sh status
```

### 监控模式（后台持续运行）
```bash
# 启用热点监控
./network_switch.sh hotspot-monitor &

# 启用安全模式监控
./network_switch.sh secure-monitor &

# 检查监控状态
./network_switch.sh check-monitor

# 停止监控
./network_switch.sh stop-monitor
```

### 护网安全功能
```bash
# 检查护网安全状态
./network_switch.sh security-check

# 强制进入最安全模式（护网专用）
./network_switch.sh force-secure

# ClashX管理
./network_switch.sh clashx-status   # 查看状态
./network_switch.sh clashx-stop     # 关闭ClashX
./network_switch.sh clashx-start    # 启动ClashX
```

### 网络测试和调试
```bash
# 测试当前网络连通性
./network_switch.sh test-network

# 扫描可用WiFi网络
./network_switch.sh scan

# 测试热点连接（不改变当前配置）
./network_switch.sh test-hotspot

# 启用调试模式查看详细日志
DEBUG_MODE=true ./network_switch.sh status
```

## ⚙️ 详细工作流程

### 网络模式总览
```mermaid
graph LR
    A[网络切换工具] --> B[热点模式<br/>个人网络]
    A --> C[有线模式<br/>办公网络]
    A --> D[安全模式<br/>双网络]

    B --> B1[WiFi + ClashX]
    C --> C1[有线 + 静态IP]
    D --> D1[智能路由分流]

    style B fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#e8f5e8
```

### 热点模式工作流程
```mermaid
graph LR
    A[启动] --> B[禁用有线] --> C[启用WiFi] --> D[连接热点]
    D --> E{成功?}
    E -->|否| F[重试5次] --> E
    E -->|是| G[清DNS缓存] --> H[启动ClashX] --> I[完成]
```

### 有线网络模式工作流程
```mermaid
graph LR
    A[启动] --> B[关闭ClashX] --> C[禁用WiFi] --> D[启用有线]
    D --> E[配置静态IP] --> F[设置DNS] --> G[清DNS缓存] --> H[完成]
```

### 安全模式工作流程
```mermaid
graph LR
    A[启动] --> B[启用有线] --> C[连接热点] --> D{双网络OK?}
    D -->|否| E[回退热点模式]
    D -->|是| F[配置智能路由] --> G[外网→热点<br/>内网→有线] --> H[完成]
```

## 📝 配置说明

### 基础网络配置
```bash
# 网络设备配置
WIFI_DEVICE="en0"           # WiFi网卡设备名
WIFI_SERVICE="Wi-Fi"        # WiFi服务名称
ETHERNET_SERVICE="AX88772A" # 有线网络服务名称
HOTSPOT_NAME="iPhone (8)"   # 热点名称
HOTSPOT_PASSWORD="12345678" # 热点密码

# 有线网络静态配置
ETHERNET_IP="***********"            # 有线网络静态IP
ETHERNET_SUBNET="*************"      # 子网掩码
ETHERNET_GATEWAY="************"      # 网关
ETHERNET_DNS="*************"         # DNS服务器
```

### 连接和监控参数
```bash
# 连接参数
CONNECT_RETRIES=5           # 连接重试次数
CONNECT_RETRY_DELAY=3       # 重试间隔（秒）
MONITOR_INTERVAL=30         # 监控检查间隔（秒）
CONNECTION_TIMEOUT=10       # 连接超时时间（秒）

# 网络测试配置
CONNECTIVITY_TEST_TIMEOUT=5           # 连通性测试超时时间（秒）
GOOGLE_TEST_URL="https://www.google.com"  # Google连通性测试URL
CLASHX_START_WAIT_TIME=3              # ClashX启动等待时间（秒）
```

### 护网安全配置
```bash
# 护网期间安全配置
SECURITY_MODE=true                    # 护网期间启用严格安全模式
FORCE_CLOSE_CLASHX_ON_ETHERNET=true   # 有线模式强制关闭ClashX
AUTO_START_CLASHX_ON_HOTSPOT=true     # 热点模式时是否自动启动ClashX
AUTO_START_CLASHX_ON_SECURE=false     # 安全模式时是否自动启动ClashX
CLASHX_CLOSE_TIMEOUT=5                # ClashX关闭超时时间（秒）
```

## 📊 状态监控和日志

### 日志系统
- **日志文件**: `~/network_switch.log`
- **PID文件**: `/tmp/network_switch_monitor.pid`
- **调试模式**: 设置 `DEBUG_MODE=true` 环境变量

### 状态检查命令
```bash
# 查看详细网络状态
./network_switch.sh status

# 检查护网安全状态
./network_switch.sh security-check

# 测试网络连通性
./network_switch.sh test-network

# 查看ClashX状态
./network_switch.sh clashx-status
```

## 🔧 技术特点

### 网络连接可靠性
- **智能重试机制**: 热点连接失败时自动重试，最多5次
- **连接状态验证**: 检查IP地址获取和网络连通性
- **DNS缓存管理**: 自动清除DNS缓存，解决切换后的连通性问题
- **网络接口刷新**: 确保网卡状态正确切换

### 智能路由管理
- **安全模式路由**: 外网流量走热点，内网流量走有线
- **护网增强路由**: 确保办公网流量强制直连，不经过代理
- **私有网段支持**: 自动配置10.0.0.0/8, **********/12, ***********/16路由
- **路由验证**: 配置后验证路由表正确性

### ClashX智能管理
- **自动启停**: 根据网络模式智能管理ClashX状态
- **多级关闭**: 优雅关闭→强制关闭→终止进程
- **启动验证**: 启动后验证进程状态和网络连通性
- **安全检查**: 护网模式下强制关闭代理软件

### 静态IP集成
- **自动配置**: 有线模式自动应用静态IP配置
- **配置验证**: 验证IP、子网掩码、网关、DNS设置
- **状态恢复**: 网卡重新启用时自动恢复静态配置

## 🛠️ 故障排除

### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 热点连接失败 | 热点名称/密码错误 | 检查配置中的HOTSPOT_NAME和HOTSPOT_PASSWORD |
| 有线网络不可用 | 服务名称错误 | 运行`networksetup -listallnetworkservices`检查 |
| DNS解析失败 | DNS缓存问题 | 运行`./network_switch_enhanced.sh test-network` |
| ClashX无法启动 | 应用未安装 | 检查ClashX是否正确安装 |
| 路由配置失败 | 权限不足 | 确保有sudo权限 |
| 静态IP配置失败 | 网络参数错误 | 检查ETHERNET_*配置参数 |

### 调试方法
```bash
# 启用详细调试模式
DEBUG_MODE=true ./network_switch.sh status

# 实时查看日志
tail -f ~/network_switch.log

# 检查网络服务列表
networksetup -listallnetworkservices

# 检查当前路由表
netstat -rn

# 检查网卡状态
ifconfig

# 测试DNS解析
nslookup google.com

# 测试网络连通性
curl -I https://www.google.com
```

## 📋 使用场景

### 护网期间
```bash
# 进入办公室，切换到安全的有线模式
./network_switch.sh ethernet

# 检查安全状态
./network_switch.sh security-check

# 强制最安全模式
./network_switch.sh force-secure
```

### 日常办公
```bash
# 在家使用热点
./network_switch.sh hotspot

# 办公室使用有线
./network_switch.sh ethernet

# 需要双网络时
./network_switch.sh secure
```

### 长期监控
```bash
# 启动热点监控（适合在家办公）
./network_switch.sh hotspot-monitor &

# 启动安全模式监控（适合办公室双网络）
./network_switch.sh secure-monitor &
```

## 🔒 安全说明

### 护网期间安全策略
1. **有线模式强制关闭ClashX**: 确保办公网络流量不经过代理
2. **安全状态实时监控**: 检测潜在的网络安全风险
3. **流量隔离**: 安全模式下办公网流量强制直连
4. **代理软件管理**: 智能管理代理软件状态

### 网络隔离原则
- **办公网络**: 直连，不经过任何代理
- **个人网络**: 可使用代理访问被墙网站
- **双网络模式**: 智能路由，确保流量正确分离

## 📖 系统要求

- **操作系统**: macOS
- **依赖工具**: networksetup, airport, ipconfig, curl
- **权限要求**:
  - 基本功能：用户权限
  - 路由配置：sudo权限
  - DNS缓存清理：sudo权限

## 👨‍💻 开发信息

- **开发者**: zhaoj296
- **设计原则**: KISS原则，保持简单有效
- **编程规范**: 统一log函数，完整错误处理
- **错误处理**: 使用`set -uo pipefail`，严格的变量检查
