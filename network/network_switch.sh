#!/bin/bash
set -uo pipefail

# =============================================================================
# 增强版网络切换工具
# 融合了原network_switch.sh和persistent_hotspot.sh的功能
# 开发者: zhaoj296
# =============================================================================

# 配置区域
WIFI_DEVICE="en0"           # WiFi网卡设备名
WIFI_SERVICE="Wi-Fi"        # WiFi服务名称
ETHERNET_SERVICE="AX88772A" # 有线网络服务名称
HOTSPOT_NAME="iPhone (8)"   # 热点名称
HOTSPOT_PASSWORD="12345678" # 热点密码

# 连接和监控配置
CONNECT_RETRIES=5           # 连接尝试次数
CONNECT_RETRY_DELAY=3       # 连接尝试间隔（秒）
MONITOR_INTERVAL=30         # 监控检查间隔（秒）
CONNECTION_TIMEOUT=10       # 连接超时时间（秒）

# 护网期间安全配置
SECURITY_MODE=true                    # 护网期间启用严格安全模式
FORCE_CLOSE_FLCLASH_ON_ETHERNET=true   # 有线模式强制关闭FlClash
AUTO_START_FLCLASH_ON_HOTSPOT=true     # 热点模式时是否自动启动FlClash
AUTO_START_FLCLASH_ON_SECURE=true      # 安全模式时是否自动启动FlClash
FORCE_CLOSE_FLCLASH_ON_SECURE=false   # 安全模式允许FlClash，但确保流量隔离
FLCLASH_CLOSE_TIMEOUT=5                # FlClash关闭超时时间（秒）

# 有线网络静态配置
ETHERNET_IP="***********"            # 有线网络静态IP
ETHERNET_SUBNET="*************"      # 子网掩码
ETHERNET_GATEWAY="************"      # 网关
ETHERNET_DNS="*************"         # DNS服务器

# 网络测试配置
CONNECTIVITY_TEST_TIMEOUT=5           # 连通性测试超时时间（秒）
GOOGLE_TEST_URL="https://www.google.com"  # Google连通性测试URL
FLCLASH_START_WAIT_TIME=3              # FlClash启动等待时间（秒）

# 代理配置
FLCLASH_HTTP_PROXY="http://127.0.0.1:1086"     # FlClash HTTP代理端口
FLCLASH_API_URL="http://127.0.0.1:9090"        # FlClash API端口
PROXY_CONFIG_FILE="$HOME/.network_switch_proxy"
SHELL_CONFIG_FILE="$HOME/.zshrc"
AUTO_CONFIG_GIT_PROXY=true                     # 自动配置Git代理
AUTO_CONFIG_HTTP_PROXY=true                    # 自动配置HTTP环境变量代理

# 日志配置
LOG_FILE="$HOME/network_switch.log"
DEBUG_MODE="${DEBUG_MODE:-false}"
PID_FILE="/tmp/network_switch_monitor.pid"

# 日志函数
log() {
  local level="$1"
  shift
  local message="$(date '+%Y-%m-%d %H:%M:%S') [$level] $*"
  echo "$message"
  echo "$message" >> "$LOG_FILE"
}

# 调试日志函数
debug_log() {
  if [[ "$DEBUG_MODE" == "true" ]]; then
    log "DEBUG" "$@"
  fi
}

# 检查依赖工具
check_dependencies() {
  local missing_tools=()

  if ! command -v networksetup >/dev/null 2>&1; then
    missing_tools+=("networksetup")
  fi

  if ! [[ -x "/System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport" ]]; then
    missing_tools+=("airport")
  fi

  if [[ ${#missing_tools[@]} -gt 0 ]]; then
    log "ERROR" "缺少必要工具: ${missing_tools[*]}"
    return 1
  fi

  return 0
}

# =============================================================================
# 网络配置管理函数
# =============================================================================

# 清除DNS缓存
flush_dns_cache() {
  log "INFO" "清除DNS缓存"
  sudo dscacheutil -flushcache 2>/dev/null || true
  sudo killall -HUP mDNSResponder 2>/dev/null || true
  sleep 1
}

# 刷新网络接口配置
refresh_network_interface() {
  local device="$1"
  if [[ -n "$device" ]]; then
    debug_log "刷新网络接口: $device"
    sudo ifconfig "$device" down 2>/dev/null || true
    sleep 1
    sudo ifconfig "$device" up 2>/dev/null || true
    sleep 1
  fi
}

# 配置有线网络静态IP
configure_ethernet_static_ip() {
  local network_interface="$ETHERNET_SERVICE"

  log "INFO" "配置有线网络静态IP: $ETHERNET_IP"

  # 设置静态IP配置
  if networksetup -setmanual "$network_interface" "$ETHERNET_IP" "$ETHERNET_SUBNET" "$ETHERNET_GATEWAY"; then
    log "INFO" "静态IP配置成功"
  else
    log "ERROR" "静态IP配置失败"
    return 1
  fi

  # 设置DNS服务器
  if networksetup -setdnsservers "$network_interface" "$ETHERNET_DNS"; then
    log "INFO" "DNS服务器配置成功: $ETHERNET_DNS"
  else
    log "ERROR" "DNS服务器配置失败"
    return 1
  fi

  # 验证配置（带重试机制）
  log "INFO" "验证静态IP配置..."
  local max_attempts=10
  local attempt=0

  while [[ $attempt -lt $max_attempts ]]; do
    sleep 1
    local current_ip=$(ipconfig getifaddr "$(get_network_device_info "$network_interface")" 2>/dev/null)

    if [[ "$current_ip" == "$ETHERNET_IP" ]]; then
      log "INFO" "有线网络静态配置验证成功: $current_ip (尝试 $((attempt + 1))/$max_attempts)"
      return 0
    fi

    attempt=$((attempt + 1))
    debug_log "IP验证尝试 $attempt/$max_attempts: 期望 $ETHERNET_IP, 当前 ${current_ip:-未获取}"
  done

  log "WARN" "Static IP verification failed after $max_attempts attempts: expected $ETHERNET_IP, actual ${current_ip:-none}"
  return 1
}

# 测试网络连通性
test_network_connectivity() {
  local test_type="${1:-basic}"  # basic, dns, google

  case "$test_type" in
    "dns")
      debug_log "测试DNS解析"
      if nslookup google.com >/dev/null 2>&1; then
        debug_log "DNS解析测试通过"
        return 0
      else
        debug_log "DNS解析测试失败"
        return 1
      fi
      ;;
    "google")
      debug_log "测试Google连通性"
      if curl -s --connect-timeout "$CONNECTIVITY_TEST_TIMEOUT" "$GOOGLE_TEST_URL" >/dev/null 2>&1; then
        debug_log "Google连通性测试通过"
        return 0
      else
        debug_log "Google连通性测试失败"
        return 1
      fi
      ;;
    "basic")
      debug_log "测试基础网络连通性"
      # 测试DNS解析
      if ! test_network_connectivity "dns"; then
        return 1
      fi
      # 测试HTTP连接
      if ! test_network_connectivity "google"; then
        return 1
      fi
      return 0
      ;;
    *)
      log "ERROR" "未知的测试类型: $test_type"
      return 1
      ;;
  esac
}

# =============================================================================
# 代理配置管理函数
# =============================================================================

# 配置持久化代理
configure_persistent_proxy() {
  log "INFO" "配置持久化代理设置"

  # 1. Git代理配置（自动持久化到 ~/.gitconfig）
  if [[ "$AUTO_CONFIG_GIT_PROXY" == "true" ]]; then
    log "INFO" "配置Git全局代理: $FLCLASH_HTTP_PROXY"
    git config --global http.proxy "$FLCLASH_HTTP_PROXY"
    git config --global https.proxy "$FLCLASH_HTTP_PROXY"
  fi

  # 2. HTTP环境变量代理（写入专用配置文件）
  if [[ "$AUTO_CONFIG_HTTP_PROXY" == "true" ]]; then
    log "INFO" "配置HTTP环境变量代理: $FLCLASH_HTTP_PROXY"

    # 创建专用代理配置文件
    cat > "$PROXY_CONFIG_FILE" << EOF
# Network Switch Tool - Proxy Configuration
# Auto-generated on $(date)
export HTTP_PROXY=$FLCLASH_HTTP_PROXY
export HTTPS_PROXY=$FLCLASH_HTTP_PROXY
export http_proxy=$FLCLASH_HTTP_PROXY
export https_proxy=$FLCLASH_HTTP_PROXY
EOF

    # 确保shell配置文件会加载代理配置
    if ! grep -q "source.*network_switch_proxy" "$SHELL_CONFIG_FILE" 2>/dev/null; then
      log "INFO" "添加代理配置加载到 $SHELL_CONFIG_FILE"
      echo "" >> "$SHELL_CONFIG_FILE"
      echo "# Network Switch Tool - Auto Proxy Configuration" >> "$SHELL_CONFIG_FILE"
      echo "source $PROXY_CONFIG_FILE 2>/dev/null || true" >> "$SHELL_CONFIG_FILE"
    fi

    # 当前会话也立即生效
    source "$PROXY_CONFIG_FILE"
  fi

  log "INFO" "代理配置完成，新终端窗口将自动加载代理设置"
}

# 清除持久化代理
clear_persistent_proxy() {
  log "INFO" "清除持久化代理设置"

  # 1. 清除Git代理
  if [[ "$AUTO_CONFIG_GIT_PROXY" == "true" ]]; then
    log "INFO" "清除Git全局代理配置"
    git config --global --unset http.proxy 2>/dev/null || true
    git config --global --unset https.proxy 2>/dev/null || true
  fi

  # 2. 清除HTTP环境变量代理
  if [[ "$AUTO_CONFIG_HTTP_PROXY" == "true" ]]; then
    log "INFO" "清除HTTP环境变量代理配置"

    # 清空代理配置文件（保留文件但清空内容）
    if [[ -f "$PROXY_CONFIG_FILE" ]]; then
      cat > "$PROXY_CONFIG_FILE" << EOF
# Network Switch Tool - Proxy Configuration
# Proxy disabled on $(date)
# No proxy configuration active
EOF
    fi

    # 当前会话也清除
    unset HTTP_PROXY HTTPS_PROXY http_proxy https_proxy
  fi

  log "INFO" "代理配置已清除，新终端窗口将不使用代理"
}

# 显示代理配置状态
show_proxy_status() {
  echo "代理配置状态:"

  # Git代理状态
  local git_http_proxy=$(git config --global --get http.proxy 2>/dev/null)
  local git_https_proxy=$(git config --global --get https.proxy 2>/dev/null)

  if [[ -n "$git_http_proxy" || -n "$git_https_proxy" ]]; then
    echo "  Git代理: ✅ 已配置"
    echo "    HTTP: ${git_http_proxy:-未设置}"
    echo "    HTTPS: ${git_https_proxy:-未设置}"
  else
    echo "  Git代理: ❌ 未配置"
  fi

  # 环境变量代理状态
  if [[ -n "${HTTP_PROXY:-}" || -n "${HTTPS_PROXY:-}" ]]; then
    echo "  环境变量代理: ✅ 已配置"
    echo "    HTTP_PROXY: ${HTTP_PROXY:-未设置}"
    echo "    HTTPS_PROXY: ${HTTPS_PROXY:-未设置}"
  else
    echo "  环境变量代理: ❌ 未配置"
  fi

  # 代理配置文件状态
  if [[ -f "$PROXY_CONFIG_FILE" ]]; then
    echo "  配置文件: ✅ 存在 ($PROXY_CONFIG_FILE)"
    if grep -q "export.*PROXY.*127.0.0.1" "$PROXY_CONFIG_FILE" 2>/dev/null; then
      echo "    状态: 包含代理配置"
    else
      echo "    状态: 已禁用代理"
    fi
  else
    echo "  配置文件: ❌ 不存在"
  fi
}

# =============================================================================
# FlClash 管理函数 (护网期间安全功能)
# =============================================================================

# 检测FlClash是否运行
is_flclash_running() {
  pgrep -f "FlClash" >/dev/null 2>&1
}

# 获取FlClash进程信息
get_flclash_process_info() {
  if is_flclash_running; then
    local pid=$(pgrep -f "FlClash")
    local process_info=$(ps -p "$pid" -o pid,ppid,comm,args 2>/dev/null)
    echo "$process_info"
  else
    echo "FlClash进程未运行"
  fi
}

# 安全关闭FlClash（护网期间专用）
secure_stop_flclash() {
  if ! is_flclash_running; then
    debug_log "FlClash未运行，无需关闭"
    return 0
  fi

  log "INFO" "护网期间安全策略：关闭FlClash以保护办公网络"

  # 记录关闭前的进程信息
  debug_log "FlClash进程信息: $(get_flclash_process_info)"

  # 方法1: 尝试优雅关闭
  log "INFO" "尝试优雅关闭FlClash应用"
  osascript -e 'quit app "FlClash"' 2>/dev/null || true

  # 等待关闭
  local count=0
  while [[ $count -lt $FLCLASH_CLOSE_TIMEOUT ]]; do
    if ! is_flclash_running; then
      log "INFO" "FlClash已优雅关闭"
      return 0
    fi
    debug_log "等待FlClash关闭... ($count/$FLCLASH_CLOSE_TIMEOUT)"
    sleep 1
    count=$((count + 1))
  done

  # 方法2: 如果还在运行，尝试强制关闭
  if is_flclash_running; then
    log "WARN" "优雅关闭失败，尝试强制关闭FlClash进程"
    pkill -f "FlClash" 2>/dev/null || true
    sleep 2
  fi

  # 方法3: 最后验证和强制终止
  if is_flclash_running; then
    log "WARN" "强制终止FlClash进程"
    pkill -9 -f "FlClash" 2>/dev/null || true
    sleep 1
  fi

  # 最终验证
  if is_flclash_running; then
    log "ERROR" "FlClash关闭失败，请手动关闭后重试"
    log "ERROR" "当前FlClash进程: $(get_flclash_process_info)"
    return 1
  else
    log "INFO" "FlClash已安全关闭"
    return 0
  fi
}

# 启动FlClash（增强版）
start_flclash() {
  if is_flclash_running; then
    debug_log "FlClash已在运行，无需启动"
    return 0
  fi

  log "INFO" "启动FlClash应用"
  if open -a "FlClash" 2>/dev/null; then
    # 等待启动
    local count=0
    while [[ $count -lt 5 ]]; do
      if is_flclash_running; then
        log "INFO" "FlClash启动成功"

        # 额外等待FlClash完全初始化
        log "INFO" "等待FlClash完全初始化..."
        sleep "$FLCLASH_START_WAIT_TIME"

        return 0
      fi
      sleep 1
      count=$((count + 1))
    done

    log "WARN" "FlClash启动可能失败，请检查应用状态"
    return 1
  else
    log "ERROR" "无法启动FlClash，请检查应用是否已安装"
    return 1
  fi
}

# 通过API启动FlClash代理功能
start_flclash_proxy_via_api() {
  debug_log "尝试通过API启动FlClash代理功能"

  local api_url="$FLCLASH_API_URL"

  # 方法1: 设置代理模式为rule
  debug_log "尝试设置FlClash模式为rule"
  if curl -s -X PATCH "$api_url/configs" \
    -H "Content-Type: application/json" \
    -d '{"mode":"rule"}' \
    --connect-timeout 3 >/dev/null 2>&1; then
    debug_log "成功设置FlClash模式为rule"
    return 0
  fi

  # 方法2: 尝试启用系统代理
  debug_log "尝试启用FlClash系统代理"
  if curl -s -X PATCH "$api_url/configs" \
    -H "Content-Type: application/json" \
    -d '{"tun":{"enable":true},"system-proxy":true}' \
    --connect-timeout 3 >/dev/null 2>&1; then
    debug_log "成功启用FlClash系统代理"
    return 0
  fi

  # 方法3: 检查并获取当前配置状态
  debug_log "检查FlClash当前配置状态"
  local current_config=$(curl -s -X GET "$api_url/configs" --connect-timeout 3 2>/dev/null)
  if [[ -n "$current_config" ]]; then
    debug_log "FlClash API可访问，当前配置: $current_config"
    # API可访问，可能代理已经在运行
    return 0
  fi

  debug_log "FlClash API调用失败"
  return 1
}

# 自动启动FlClash代理功能
auto_start_flclash_proxy() {
  log "INFO" "尝试自动启动FlClash代理功能"

  # 等待FlClash完全初始化
  sleep 2

  # 优先尝试API调用
  if start_flclash_proxy_via_api; then
    log "INFO" "通过API成功启动FlClash代理"
    return 0
  else
    log "WARN" "API调用失败，FlClash可能需要手动点击启动按钮"
    log "INFO" "请在FlClash界面点击右下角的播放按钮启动代理"
    return 1
  fi
}

# 启动FlClash并验证网络
start_flclash_and_verify() {
  log "INFO" "启动FlClash并验证网络连通性"

  if ! start_flclash; then
    log "ERROR" "FlClash启动失败"
    return 1
  fi

  # 尝试自动启动代理功能
  auto_start_flclash_proxy

  # 测试网络连通性
  log "INFO" "测试网络连通性..."
  if test_network_connectivity "google"; then
    log "INFO" "网络连通性验证成功，可以访问Google"
    return 0
  else
    log "WARN" "无法访问Google，可能需要手动检查FlClash配置"
    # 不返回失败，因为可能是FlClash配置问题，不是启动问题
    return 0
  fi
}

# 显示FlClash状态
show_flclash_status() {
  echo "FlClash状态信息:"
  if is_flclash_running; then
    echo "  运行状态: ✅ 运行中"
    echo "  进程信息:"
    get_flclash_process_info | sed 's/^/    /'
  else
    echo "  运行状态: ❌ 未运行"
  fi
}

# 显示FlClash配置建议
show_flclash_config_suggestions() {
  echo "FlClash配置建议:"
  echo "  为确保安全模式下的流量隔离，建议在FlClash配置文件rules部分最前面添加："
  echo ""
  echo "  rules:"
  echo "    # 关键内网段（最高优先级）"
  echo "    - 'IP-CIDR,**********/16,DIRECT,no-resolve'"
  echo "    - 'IP-CIDR,**********/16,DIRECT,no-resolve'"
  echo "    - 'IP-CIDR,**********/24,DIRECT,no-resolve'"
  echo "    - 'IP-CIDR,**********/24,DIRECT,no-resolve'"
  echo "    # 您现有的所有规则保持不变..."
  echo ""
  echo "  配置后重启FlClash使规则生效。"
}

# VPN连接诊断
diagnose_vpn_connection() {
  local vpn_server="************"

  echo "VPN连接诊断报告:"
  echo "=================="

  # 1. VPN服务器路由检查
  echo "1. VPN服务器路由检查:"
  local route_info=$(route get "$vpn_server" 2>/dev/null)
  local interface=$(echo "$route_info" | grep "interface:" | awk '{print $2}')
  local gateway=$(echo "$route_info" | grep "gateway:" | awk '{print $2}')

  echo "   服务器: $vpn_server"
  echo "   接口: $interface"
  echo "   网关: $gateway"

  local eth_device=$(get_network_device_info "$ETHERNET_SERVICE")
  if [[ "$interface" == "$eth_device" ]]; then
    echo "   状态: ✅ 路由正确（走有线网卡）"
  else
    echo "   状态: ❌ 路由错误（应该走$eth_device）"
  fi

  # 2. 网络连通性测试
  echo ""
  echo "2. 网络连通性测试:"
  if ping -c 1 -W 3000 "$vpn_server" >/dev/null 2>&1; then
    echo "   Ping测试: ✅ 可达"
  else
    echo "   Ping测试: ❌ 不可达"
  fi

  # 3. 相关路由表
  echo ""
  echo "3. 相关路由表:"
  netstat -rn | grep -E "(10\.64\.46|10\.3\.224)" | sed 's/^/   /'

  # 4. 建议
  echo ""
  echo "4. 故障排除建议:"
  echo "   - 确保在secure模式下连接VPN"
  echo "   - 检查VPN客户端是否有网络接口绑定设置"
  echo "   - 尝试重新启动secure模式: ./network_switch.sh secure"
  echo "   - 如果仍有问题，可能需要在VPN客户端中指定网络接口"
}

# 检测系统代理设置
check_system_proxy() {
  local http_proxy=$(networksetup -getwebproxy "Wi-Fi" 2>/dev/null)
  local https_proxy=$(networksetup -getsecurewebproxy "Wi-Fi" 2>/dev/null)
  local socks_proxy=$(networksetup -getsocksfirewallproxy "Wi-Fi" 2>/dev/null)

  if echo "$http_proxy" | grep -q "Enabled: Yes" ||
     echo "$https_proxy" | grep -q "Enabled: Yes" ||
     echo "$socks_proxy" | grep -q "Enabled: Yes"; then
    return 0  # 有系统代理
  else
    return 1  # 无系统代理
  fi
}

# 护网安全检查
verify_network_security() {
  local mode="$1"
  local security_issues=()

  # 检查FlClash状态
  if is_flclash_running; then
    if [[ "$mode" == "ethernet" || "$mode" == "secure" ]]; then
      security_issues+=("FlClash在办公网络模式下运行")
    fi
  fi

  # 检查系统代理
  if check_system_proxy; then
    security_issues+=("检测到系统代理设置")
  fi

  # 检查网络模式
  local wifi_enabled=$(get_network_status "$WIFI_SERVICE")
  local eth_enabled=$(get_network_status "$ETHERNET_SERVICE")

  if [[ "$mode" == "secure" && "$wifi_enabled" == "Enabled" && "$eth_enabled" == "Enabled" ]]; then
    # 检查关键内网段路由配置
    if ! check_critical_internal_routes; then
      security_issues+=("关键内网段路由配置可能不完整")
    fi

    # 检查FlClash状态
    if is_flclash_running; then
      debug_log "FlClash运行中，应用层分流生效"
    else
      security_issues+=("FlClash未运行，缺少应用层分流保护")
    fi
  fi

  # 输出安全检查结果
  if [[ ${#security_issues[@]} -gt 0 ]]; then
    echo "⚠️  发现安全风险:"
    for issue in "${security_issues[@]}"; do
      echo "    - $issue"
    done
    return 1
  else
    echo "✅ 网络安全检查通过"
    return 0
  fi
}

# 检查关键内网段路由配置
check_critical_internal_routes() {
  local eth_gateway=$(get_network_gateway "$(get_network_device_info "$ETHERNET_SERVICE")")
  local critical_networks=("**********/16" "**********/16" "**********/24" "**********/24")

  if [[ -z "$eth_gateway" ]]; then
    debug_log "无法获取有线网关，跳过内网路由检查"
    return 1
  fi

  for network in "${critical_networks[@]}"; do
    local route_gateway=$(netstat -rn | grep "^${network%/*}" | awk '{print $2}' | head -1)
    if [[ -n "$route_gateway" && "$route_gateway" != "$eth_gateway" ]]; then
      debug_log "关键内网段 $network 路由异常: $route_gateway (期望: $eth_gateway)"
      return 1
    fi
  done

  # 检查默认路由是否走热点（让FlClash处理分流）
  local wifi_gateway=$(get_network_gateway "$WIFI_DEVICE")
  local default_route=$(netstat -rn | grep "^default" | head -1 | awk '{print $2}')
  if [[ "$default_route" != "$wifi_gateway" ]]; then
    debug_log "默认路由检查失败: default -> $default_route (期望: $wifi_gateway)"
    return 1
  fi

  debug_log "关键内网段路由配置检查通过"
  return 0
}

# 检查VPN服务器路由
check_vpn_server_route() {
  local vpn_server="************"
  local eth_device=$(get_network_device_info "$ETHERNET_SERVICE")

  debug_log "检查VPN服务器路由：$vpn_server"

  local route_info=$(route get "$vpn_server" 2>/dev/null)
  local interface=$(echo "$route_info" | grep "interface:" | awk '{print $2}')
  local gateway=$(echo "$route_info" | grep "gateway:" | awk '{print $2}')

  if [[ "$interface" == "$eth_device" ]]; then
    debug_log "VPN服务器路由正确：$vpn_server -> $interface"
    return 0
  else
    debug_log "VPN服务器路由异常：$vpn_server -> $interface (期望: $eth_device)"
    return 1
  fi
}

# 配置VPN接口绑定（强制VPN使用指定网络接口）
configure_vpn_interface_binding() {
  local target_interface="$1"
  local vpn_server="************"

  log "INFO" "配置VPN接口绑定：强制VPN使用 $target_interface"

  # 1. 确保VPN服务器的host路由存在且正确
  local current_route=$(route get "$vpn_server" 2>/dev/null | grep "interface:" | awk '{print $2}')
  if [[ "$current_route" != "$target_interface" ]]; then
    log "WARN" "VPN服务器路由接口不匹配，重新配置"
    local eth_gateway=$(get_network_gateway "$target_interface")
    sudo route delete -host "$vpn_server" >/dev/null 2>&1 || true
    sudo route add -host "$vpn_server" "$eth_gateway" >/dev/null 2>&1
  fi

  # 2. 配置VPN网段的接口绑定路由
  local eth_gateway=$(get_network_gateway "$target_interface")
  if [[ -n "$eth_gateway" ]]; then
    # 强制所有VPN相关网段走指定接口
    sudo route add -net **********/16 "$eth_gateway" -interface "$target_interface" >/dev/null 2>&1 || true
    sudo route add -net **********/16 "$eth_gateway" -interface "$target_interface" >/dev/null 2>&1 || true
    sudo route add -net **********/24 "$eth_gateway" -interface "$target_interface" >/dev/null 2>&1 || true

    log "INFO" "VPN网段接口绑定已配置：所有VPN流量 -> $target_interface"
  else
    log "ERROR" "无法获取 $target_interface 的网关地址"
    return 1
  fi

  # 3. 设置网络服务优先级（确保VPN使用有线网络）
  configure_network_service_priority_for_vpn

  log "INFO" "VPN接口绑定配置完成"
  return 0
}

# 配置网络服务优先级（VPN专用）
configure_network_service_priority_for_vpn() {
  log "INFO" "配置网络服务优先级：VPN优先使用有线网络"

  # 临时调整网络服务顺序，让有线网络在VPN连接时优先
  # 注意：这不会影响FlClash的热点使用，因为FlClash在应用层工作
  local current_order=$(networksetup -listnetworkserviceorder | grep -E "(AX88772A|Wi-Fi)" | sed 's/^[^)]*) //')

  if echo "$current_order" | head -1 | grep -q "Wi-Fi"; then
    log "INFO" "当前WiFi优先，为VPN连接临时调整网络优先级"
    # 创建临时的网络服务顺序：有线优先（仅用于VPN连接）
    # 这个设置主要影响系统级网络连接，不影响应用层的FlClash
    networksetup -ordernetworkservices "AX88772A" "Wi-Fi" "Thunderbolt Bridge" >/dev/null 2>&1 || true
    log "INFO" "网络优先级已临时调整：有线网络优先（VPN连接专用）"
    log "INFO" "注意：此调整不影响FlClash的热点代理功能"
  else
    log "INFO" "网络优先级已正确：有线网络优先"
  fi
}

# 恢复默认网络服务优先级（热点优先）
restore_default_network_priority() {
  log "INFO" "恢复默认网络优先级：热点优先"

  # 恢复用户偏好的网络服务顺序：WiFi优先
  networksetup -ordernetworkservices "Wi-Fi" "AX88772A" "Thunderbolt Bridge" >/dev/null 2>&1 || true
  log "INFO" "网络优先级已恢复：WiFi优先（用户偏好设置）"
}

# =============================================================================
# 安全模式验证函数
# =============================================================================

# 验证Google路由走向
verify_google_route() {
  debug_log "验证Google访问路由"

  # 获取Google IP
  local google_ip=$(nslookup google.com 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}')

  if [[ -n "$google_ip" && "$google_ip" != "127.0.0.1" ]]; then
    # 检查路由
    local route_info=$(route get "$google_ip" 2>/dev/null)
    local interface=$(echo "$route_info" | grep "interface:" | awk '{print $2}')

    if [[ "$interface" == "$WIFI_DEVICE" ]]; then
      debug_log "Google traffic route correct: via $WIFI_DEVICE interface"
      return 0
    else
      debug_log "Google traffic route via $interface interface (expected $WIFI_DEVICE)"
      return 1
    fi
  else
    debug_log "无法解析Google IP地址"
    return 1
  fi
}

# 验证FlClash代理访问
verify_flclash_proxy_access() {
  debug_log "验证FlClash代理访问Google"

  # 1. 检查FlClash是否运行
  if ! is_flclash_running; then
    debug_log "FlClash未运行，无法进行代理验证"
    return 1
  fi

  # 2. 通过FlClash代理访问Google
  if curl -x http://127.0.0.1:1086 -s --connect-timeout "$CONNECTIVITY_TEST_TIMEOUT" https://www.google.com >/dev/null 2>&1; then
    debug_log "通过FlClash代理访问Google成功"
  else
    debug_log "通过FlClash代理访问Google失败"
    return 1
  fi

  # 3. 验证FlClash API可访问性（可选）
  local flclash_api="http://127.0.0.1:9090"
  if curl -s --connect-timeout 2 "$flclash_api/version" >/dev/null 2>&1; then
    debug_log "FlClash API可访问，代理工作正常"
  else
    debug_log "FlClash API不可访问，但代理功能正常"
  fi

  return 0
}

# 安全模式完整验证
verify_secure_mode_complete() {
  log "INFO" "执行安全模式完整验证"

  local verification_passed=true
  local step=1
  local total_steps=4

  # 1. 基础网络连通性
  log "INFO" "[$step/$total_steps] 验证基础网络连通性"
  if test_network_connectivity "dns"; then
    log "INFO" "✅ DNS解析正常"
  else
    log "ERROR" "❌ DNS解析失败"
    verification_passed=false
  fi
  step=$((step + 1))

  # 2. FlClash代理功能
  log "INFO" "[$step/$total_steps] 验证FlClash代理功能"
  if verify_flclash_proxy_access; then
    log "INFO" "✅ FlClash代理访问Google成功"
  else
    log "ERROR" "❌ FlClash代理访问Google失败"
    verification_passed=false
  fi
  step=$((step + 1))

  # 3. 关键内网段路由配置
  log "INFO" "[$step/$total_steps] 验证关键内网段路由"
  if check_critical_internal_routes; then
    log "INFO" "✅ 关键内网段路由配置正确"
  else
    log "ERROR" "❌ 关键内网段路由配置异常"
    verification_passed=false
  fi

  # 3.1 VPN服务器路由检查
  if check_vpn_server_route; then
    log "INFO" "✅ VPN服务器路由配置正确"
  else
    log "WARN" "⚠️  VPN服务器路由需要确认"
  fi
  step=$((step + 1))

  # 4. Google流量走向验证
  log "INFO" "[$step/$total_steps] 验证Google流量走向"
  if verify_google_route; then
    log "INFO" "✅ Google流量正确走热点网卡"
  else
    log "WARN" "⚠️  Google流量走向需要确认（可能通过代理正常）"
    # 这里不设为失败，因为通过代理访问也是正常的
  fi

  # 验证总结
  echo ""
  if [[ "$verification_passed" == "true" ]]; then
    log "INFO" "🎉 安全模式验证通过！"
    log "INFO" "✅ 可以安全使用Augment等服务"
    log "INFO" "✅ 内网流量已正确隔离到有线网卡"
    log "INFO" "✅ 外网流量通过热点+代理访问"
    return 0
  else
    log "ERROR" "❌ 安全模式验证失败，存在安全风险"
    log "ERROR" "建议检查网络配置或使用其他模式"
    return 1
  fi
}

# 获取当前网络状态
get_network_status() {
  local service="$1"
  if [[ -z "$service" ]]; then
    echo "Not Available"
    return 1
  fi
  
  networksetup -getnetworkserviceenabled "$service" 2>/dev/null || echo "Unknown"
}

# 启用/禁用网络服务
toggle_network_service() {
  local service="$1"
  local action="$2" # on 或 off
  
  if [[ -z "$service" ]]; then
    log "WARN" "服务名称为空，跳过 $action 操作"
    return 0
  fi
  
  # 检查服务是否存在
  if ! networksetup -listallnetworkservices | grep -q "$service"; then
    log "WARN" "服务 $service 不存在，跳过 $action 操作"
    return 0
  fi
  
  log "INFO" "将 $service 设置为 $action"
  networksetup -setnetworkserviceenabled "$service" "$action"
  return $?
}

# 检查热点是否可见
is_hotspot_visible() {
  local hotspot_name="$1"
  debug_log "扫描可用网络，查找热点: $hotspot_name"
  
  local scan_result=$(/System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport -s 2>/dev/null | grep "$hotspot_name")
  if [[ -n "$scan_result" ]]; then
    debug_log "热点可见: $scan_result"
    return 0
  else
    debug_log "热点不可见"
    return 1
  fi
}

# 验证WiFi连接状态
verify_wifi_connection() {
  local expected_network="$1"
  local timeout="${2:-$CONNECTION_TIMEOUT}"

  debug_log "Verifying WiFi connection, expected: $expected_network, timeout: ${timeout}s"

  local count=0
  local current_wifi=""
  local wifi_ip=""

  while [[ $count -lt $timeout ]]; do
    current_wifi=$(networksetup -getairportnetwork "$WIFI_DEVICE" 2>/dev/null | cut -d':' -f2 | xargs)
    wifi_ip=$(ipconfig getifaddr "$WIFI_DEVICE" 2>/dev/null)

    if [[ "$current_wifi" == "$expected_network" && -n "$wifi_ip" ]]; then
      log "INFO" "WiFi连接验证成功: $expected_network, IP: $wifi_ip"
      return 0
    fi

    debug_log "等待连接建立... ($count/$timeout)"
    sleep 1
    count=$((count + 1))
  done

  log "ERROR" "WiFi connection verification failed: expected $expected_network, current $current_wifi"
  return 1
}

# 尝试连接热点（单次尝试）
try_connect_hotspot() {
  local network_name="$1"
  local password="$2"

  debug_log "尝试连接热点: $network_name"

  # 确保WiFi已打开
  local wifi_power=$(networksetup -getairportpower "$WIFI_DEVICE" 2>/dev/null | awk '{print $4}')
  if [[ "$wifi_power" != "On" ]]; then
    log "INFO" "WiFi已关闭，正在打开"
    networksetup -setairportpower "$WIFI_DEVICE" on
    sleep 2
  fi

  # 检查热点是否可见
  if is_hotspot_visible "$network_name"; then
    log "INFO" "热点可见，正在连接"
  else
    log "INFO" "热点不可见，尝试直接连接 (这可能会激活iPhone热点)"
  fi

  # 尝试连接
  if [[ -n "$password" ]]; then
    networksetup -setairportnetwork "$WIFI_DEVICE" "$network_name" "$password"
  else
    networksetup -setairportnetwork "$WIFI_DEVICE" "$network_name"
  fi

  # 验证连接
  if verify_wifi_connection "$network_name"; then
    return 0
  else
    return 1
  fi
}

# 增强的WiFi连接函数（带重试机制）
connect_wifi_enhanced() {
  local network_name="$1"
  local password="$2"
  local max_retries="${3:-$CONNECT_RETRIES}"

  log "INFO" "开始连接WiFi: $network_name (最大重试次数: $max_retries)"

  local retry=0
  while [[ $retry -lt $max_retries ]]; do
    if try_connect_hotspot "$network_name" "$password"; then
      log "INFO" "WiFi连接成功: $network_name"
      return 0
    fi

    retry=$((retry + 1))
    if [[ $retry -lt $max_retries ]]; then
      log "WARN" "连接尝试 $retry/$max_retries 失败，等待后重试..."
      sleep $CONNECT_RETRY_DELAY
    fi
  done

  log "ERROR" "WiFi连接失败，已达到最大重试次数: $max_retries"
  return 1
}

# 检查有线网络是否可用
check_ethernet_available() {
  # 检查服务是否存在
  if ! networksetup -listallnetworkservices | grep -q "$ETHERNET_SERVICE"; then
    debug_log "有线网络服务 $ETHERNET_SERVICE 不存在"
    return 1
  fi

  # 检查服务是否启用
  if [[ "$(get_network_status "$ETHERNET_SERVICE")" != "Enabled" ]]; then
    debug_log "有线网络服务 $ETHERNET_SERVICE 未启用"
    return 1
  fi

  # 尝试获取设备名
  local device=$(networksetup -listallhardwareports | grep -A 1 "$ETHERNET_SERVICE" | grep "Device:" | awk '{print $2}')
  if [[ -z "$device" ]]; then
    debug_log "无法获取有线网络设备名"
    return 1
  fi

  # 检查是否有IP地址
  local ip=$(ipconfig getifaddr "$device" 2>/dev/null)
  if [[ -z "$ip" ]]; then
    debug_log "有线网络无IP地址，可能未连接"
    return 1
  fi

  log "INFO" "有线网络可用，设备: $device, IP: $ip"
  return 0
}

# 获取网络设备信息
get_network_device_info() {
  local service="$1"
  local device=$(networksetup -listallhardwareports | grep -A 1 "$service" | grep "Device:" | awk '{print $2}')
  echo "$device"
}

# 获取网络IP地址
get_network_ip() {
  local device="$1"
  if [[ -n "$device" ]]; then
    ipconfig getifaddr "$device" 2>/dev/null
  fi
}

# 获取网络网关
get_network_gateway() {
  local device="$1"
  if [[ -n "$device" ]]; then
    netstat -rn | grep -A 1 "$device" | grep default | awk '{print $2}' | head -1
  fi
}

# 配置路由表
configure_routing() {
  local wifi_device="$WIFI_DEVICE"
  local eth_device=$(get_network_device_info "$ETHERNET_SERVICE")

  local wifi_ip=$(get_network_ip "$wifi_device")
  local eth_ip=$(get_network_ip "$eth_device")

  if [[ -n "$wifi_ip" && -n "$eth_ip" ]]; then
    log "INFO" "配置路由表优先使用热点网络访问互联网"

    # 获取默认网关
    local wifi_gateway=$(get_network_gateway "$wifi_device")
    local eth_gateway=$(get_network_gateway "$eth_device")

    debug_log "WiFi网关: $wifi_gateway, 有线网关: $eth_gateway"

    if [[ -n "$wifi_gateway" ]]; then
      # 删除现有默认路由
      log "INFO" "删除现有默认路由"
      sudo route delete default >/dev/null 2>&1 || true

      # 添加新的默认路由，优先使用热点
      log "INFO" "添加新的默认路由，优先使用热点"
      sudo route add default "$wifi_gateway"

      # 如果有有线网关，添加内网路由走有线网卡
      if [[ -n "$eth_gateway" ]]; then
        log "INFO" "添加内网路由走有线网卡"
        sudo route add -net 10.0.0.0/8 "$eth_gateway" >/dev/null 2>&1 || true
        sudo route add -net **********/12 "$eth_gateway" >/dev/null 2>&1 || true
        sudo route add -net ***********/16 "$eth_gateway" >/dev/null 2>&1 || true
      fi

      log "INFO" "路由表配置完成"
      return 0
    else
      log "ERROR" "无法获取WiFi网关地址"
      return 1
    fi
  else
    log "ERROR" "无法获取网络IP地址 - WiFi: $wifi_ip, 有线: $eth_ip"
    return 1
  fi
}

# 最小化安全路由配置（FlClash主导 + 系统兜底）
configure_secure_routing() {
  local wifi_device="$WIFI_DEVICE"
  local eth_device=$(get_network_device_info "$ETHERNET_SERVICE")

  local wifi_ip=$(get_network_ip "$wifi_device")
  local eth_ip=$(get_network_ip "$eth_device")

  if [[ -n "$wifi_ip" && -n "$eth_ip" ]]; then
    log "INFO" "配置安全路由：FlClash主导分流 + 系统路由兜底"

    # 获取默认网关
    local wifi_gateway=$(get_network_gateway "$wifi_device")
    local eth_gateway=$(get_network_gateway "$eth_device")

    debug_log "WiFi网关: $wifi_gateway, 有线网关: $eth_gateway"

    if [[ -n "$wifi_gateway" && -n "$eth_gateway" ]]; then
      # 设置默认路由走热点（让FlClash处理大部分流量）
      log "INFO" "设置默认路由走热点（FlClash将处理分流）"
      sudo route delete default >/dev/null 2>&1 || true
      sudo route add default "$wifi_gateway"

      # 只配置关键内网段的兜底路由（防止FlClash配置遗漏）
      log "INFO" "配置关键内网段兜底路由（安全保障）"
      sudo route add -net **********/16 "$eth_gateway" >/dev/null 2>&1 || true  # VPN网段
      sudo route add -net **********/16 "$eth_gateway" >/dev/null 2>&1 || true  # VPN网段
      sudo route add -net **********/24 "$eth_gateway" >/dev/null 2>&1 || true  # VPN网段
      sudo route add -net **********/24 "$eth_gateway" >/dev/null 2>&1 || true  # 办公网段

      # 强化VPN服务器路由（确保VPN连接走有线网卡）
      sudo route add -host ************ "$eth_gateway" >/dev/null 2>&1 || true
      log "INFO" "VPN服务器专用路由已配置：************ -> 有线网卡"

      # 配置VPN接口绑定（强制VPN流量走有线网卡）
      configure_vpn_interface_binding "$eth_device"

      log "INFO" "安全路由配置完成"
      log "INFO" "流量分流策略：FlClash应用层分流 + 关键内网段系统路由兜底"
      return 0
    else
      log "ERROR" "无法获取网关地址 - WiFi: $wifi_gateway, 有线: $eth_gateway"
      return 1
    fi
  else
    log "ERROR" "无法获取网络IP地址 - WiFi: $wifi_ip, 有线: $eth_ip"
    return 1
  fi
}

# 启用热点模式（增强版）
enable_hotspot_mode() {
  log "INFO" "启用热点模式"

  # 禁用有线网络（先禁用避免冲突）
  if networksetup -listallnetworkservices | grep -q "$ETHERNET_SERVICE"; then
    toggle_network_service "$ETHERNET_SERVICE" off
  else
    log "INFO" "未检测到有线网络服务，跳过禁用操作"
  fi

  # 确保WiFi服务已启用
  toggle_network_service "$WIFI_SERVICE" on

  # 使用增强的连接函数连接热点
  if connect_wifi_enhanced "$HOTSPOT_NAME" "$HOTSPOT_PASSWORD"; then
    log "INFO" "热点连接成功"
  else
    log "ERROR" "热点连接失败"
    return 1
  fi

  # 清除DNS缓存，解决DNS冲突问题
  flush_dns_cache

  # 恢复默认网络优先级（热点优先）
  restore_default_network_priority

  # 验证热点连接和IP获取
  local wifi_ip=$(get_network_ip "$WIFI_DEVICE")
  if [[ -z "$wifi_ip" ]]; then
    log "ERROR" "热点IP获取失败"
    return 1
  fi

  log "INFO" "热点IP获取成功: $wifi_ip"

  # 智能FlClash管理（个人网络环境）
  if [[ "$AUTO_START_FLCLASH_ON_HOTSPOT" == "true" ]]; then
    log "INFO" "热点模式：启动FlClash并验证网络"
    if start_flclash_and_verify; then
      log "INFO" "FlClash启动并验证成功"
      # 配置持久化代理
      configure_persistent_proxy
    else
      log "WARN" "FlClash启动或验证失败，请手动检查"
    fi
  else
    # 即使不自动启动，也测试基础连通性
    log "INFO" "测试基础网络连通性"
    if test_network_connectivity "dns"; then
      log "INFO" "DNS解析正常"

      # 如果FlClash未运行，提醒用户
      if ! is_flclash_running; then
        log "WARN" "FlClash未运行，可能无法访问被墙网站"
        log "INFO" "建议运行: $0 flclash-start"
      fi
    else
      log "WARN" "DNS解析异常，请检查网络配置"
    fi
  fi

  log "INFO" "热点模式启用完成"
}

# 启用有线网络模式（护网安全增强版）
enable_ethernet_mode() {
  log "INFO" "启用有线网络模式（护网安全模式）"

  # 清除代理配置（在关闭FlClash之前）
  clear_persistent_proxy

  # 护网期间安全策略：必须先关闭FlClash
  if [[ "$FORCE_CLOSE_FLCLASH_ON_ETHERNET" == "true" ]]; then
    if ! secure_stop_flclash; then
      log "ERROR" "无法关闭FlClash，为了网络安全，请手动关闭后重试"
      log "ERROR" "护网期间不允许在有线网络模式下运行代理软件"
      return 1
    fi
  fi

  # 禁用WiFi（先禁用避免冲突）
  toggle_network_service "$WIFI_SERVICE" off

  # 启用有线网络
  toggle_network_service "$ETHERNET_SERVICE" on

  # 等待网络服务启动（USB网卡需要更长时间）
  log "INFO" "等待有线网卡初始化..."
  sleep 5

  # 配置静态IP
  log "INFO" "配置有线网络静态IP"
  if ! configure_ethernet_static_ip; then
    log "ERROR" "静态IP配置失败"
    return 1
  fi

  # 刷新网络接口
  local eth_device=$(get_network_device_info "$ETHERNET_SERVICE")
  if [[ -n "$eth_device" ]]; then
    refresh_network_interface "$eth_device"
  fi

  # 清除DNS缓存
  flush_dns_cache

  # 验证网络连接
  sleep 3
  local eth_ip=$(get_network_ip "$eth_device")
  if [[ -z "$eth_ip" ]]; then
    log "ERROR" "有线网络IP获取失败，请检查网络连接"
    return 1
  fi

  # 护网期间额外安全检查
  if [[ "$SECURITY_MODE" == "true" ]]; then
    log "INFO" "执行护网期间安全检查"

    # 确认FlClash已关闭
    if is_flclash_running; then
      log "ERROR" "安全检查失败：FlClash仍在运行"
      return 1
    fi

    log "INFO" "有线网络连接正常，IP: $eth_ip"
    log "INFO" "护网安全模式：网络流量将直连，不经过任何代理"

    # 测试基础网络连通性
    if test_network_connectivity "dns"; then
      log "INFO" "网络连通性验证成功"
    else
      log "WARN" "网络连通性验证失败，请检查网络配置"
    fi
  fi

  log "INFO" "有线网络模式启用完成（护网安全模式已激活）"
}

# 启用安全模式（热点+有线，护网增强版）
enable_secure_mode() {
  log "INFO" "启用安全模式 (热点+有线，护网增强)"

  # 护网期间安全策略：智能FlClash管理
  log "INFO" "安全模式：启动FlClash实现应用层分流"
  if ! is_flclash_running; then
    if start_flclash_and_verify; then
      log "INFO" "FlClash启动成功，应用层分流已激活"
      # 配置持久化代理
      configure_persistent_proxy
    else
      log "WARN" "FlClash启动失败，将依赖系统路由进行流量隔离"
    fi
  else
    log "INFO" "FlClash已运行，应用层分流正常"
    # FlClash已运行，确保代理配置存在
    configure_persistent_proxy
  fi

  # 检查有线网络是否可用
  if ! check_ethernet_available; then
    log "WARN" "有线网络不可用，将只启用热点"
    enable_hotspot_mode
    return
  fi

  # 启用有线网络
  toggle_network_service "$ETHERNET_SERVICE" on

  # 启用WiFi并连接热点
  toggle_network_service "$WIFI_SERVICE" on
  if ! connect_wifi_enhanced "$HOTSPOT_NAME" "$HOTSPOT_PASSWORD"; then
    log "ERROR" "热点连接失败，安全模式启用失败"
    return 1
  fi

  # 清除DNS缓存，确保路由生效
  flush_dns_cache

  # 配置护网增强路由表
  if configure_secure_routing; then
    log "INFO" "安全模式路由配置完成"

    # 验证双网络连接
    local wifi_ip=$(get_network_ip "$WIFI_DEVICE")
    local eth_device=$(get_network_device_info "$ETHERNET_SERVICE")
    local eth_ip=$(get_network_ip "$eth_device")

    log "INFO" "双网络连接状态 - WiFi: $wifi_ip, 有线: $eth_ip"

    # 执行完整的安全模式验证
    echo ""
    log "INFO" "开始安全模式验证..."
    if verify_secure_mode_complete; then
      log "INFO" "安全模式启用并验证完成 ✅"
    else
      log "WARN" "安全模式启用完成，但验证发现问题，请检查配置 ⚠️"
    fi
  else
    log "ERROR" "路由配置失败，安全模式启用失败"
    return 1
  fi
}

# 检查网络连接状态
check_network_connection() {
  local mode="$1"

  case "$mode" in
    "hotspot")
      local current_wifi=$(networksetup -getairportnetwork "$WIFI_DEVICE" 2>/dev/null | cut -d':' -f2 | xargs)
      local wifi_ip=$(get_network_ip "$WIFI_DEVICE")

      if [[ "$current_wifi" == "$HOTSPOT_NAME" && -n "$wifi_ip" ]]; then
        debug_log "热点连接正常: $HOTSPOT_NAME, IP: $wifi_ip"
        return 0
      else
        log "WARN" "热点连接异常: 期望 $HOTSPOT_NAME，当前 $current_wifi"
        return 1
      fi
      ;;
    "ethernet")
      if check_ethernet_available; then
        return 0
      else
        log "WARN" "有线网络连接异常"
        return 1
      fi
      ;;
    "secure")
      local wifi_ok=false
      local eth_ok=false

      # 检查热点连接
      local current_wifi=$(networksetup -getairportnetwork "$WIFI_DEVICE" 2>/dev/null | cut -d':' -f2 | xargs)
      local wifi_ip=$(get_network_ip "$WIFI_DEVICE")
      if [[ "$current_wifi" == "$HOTSPOT_NAME" && -n "$wifi_ip" ]]; then
        wifi_ok=true
      fi

      # 检查有线连接
      if check_ethernet_available; then
        eth_ok=true
      fi

      if [[ "$wifi_ok" == true && "$eth_ok" == true ]]; then
        debug_log "安全模式连接正常"
        return 0
      else
        log "WARN" "安全模式连接异常 - WiFi: $wifi_ok, 有线: $eth_ok"
        return 1
      fi
      ;;
    *)
      log "ERROR" "未知的网络模式: $mode"
      return 1
      ;;
  esac
}

# 修复网络连接
repair_network_connection() {
  local mode="$1"

  log "INFO" "尝试修复网络连接: $mode"

  case "$mode" in
    "hotspot")
      enable_hotspot_mode
      ;;
    "ethernet")
      enable_ethernet_mode
      ;;
    "secure")
      enable_secure_mode
      ;;
    *)
      log "ERROR" "未知的网络模式: $mode"
      return 1
      ;;
  esac
}

# 监控网络连接
monitor_network_connection() {
  local mode="$1"

  log "INFO" "开始监控网络连接: $mode"
  log "INFO" "监控间隔: $MONITOR_INTERVAL 秒"
  log "INFO" "PID文件: $PID_FILE"

  # 保存当前进程PID
  echo $$ > "$PID_FILE"

  # 设置信号处理
  trap 'cleanup_monitor' EXIT INT TERM

  # 首次建立连接
  if ! repair_network_connection "$mode"; then
    log "ERROR" "初始网络连接建立失败"
    return 1
  fi

  # 持续监控循环
  while true; do
    sleep "$MONITOR_INTERVAL"

    if ! check_network_connection "$mode"; then
      log "WARN" "检测到网络连接问题，尝试修复"
      repair_network_connection "$mode"
    else
      debug_log "网络连接检查正常: $mode"
    fi
  done
}

# 清理监控进程
cleanup_monitor() {
  log "INFO" "清理监控进程"
  if [[ -f "$PID_FILE" ]]; then
    rm -f "$PID_FILE"
  fi
  exit 0
}

# 停止监控进程
stop_monitor() {
  if [[ -f "$PID_FILE" ]]; then
    local pid=$(cat "$PID_FILE")
    if kill -0 "$pid" 2>/dev/null; then
      log "INFO" "停止监控进程: $pid"
      kill "$pid"
      rm -f "$PID_FILE"
      return 0
    else
      log "WARN" "监控进程不存在或已停止"
      rm -f "$PID_FILE"
      return 1
    fi
  else
    log "INFO" "未找到监控进程PID文件"
    return 1
  fi
}

# 检查监控进程状态
check_monitor_status() {
  if [[ -f "$PID_FILE" ]]; then
    local pid=$(cat "$PID_FILE")
    if kill -0 "$pid" 2>/dev/null; then
      echo "监控进程运行中 (PID: $pid)"
      return 0
    else
      echo "监控进程PID文件存在但进程已停止"
      rm -f "$PID_FILE"
      return 1
    fi
  else
    echo "监控进程未运行"
    return 1
  fi
}

# 显示增强的网络状态
show_enhanced_status() {
  echo "=========================================="
  echo "网络切换工具 - 当前状态"
  echo "=========================================="

  # 显示WiFi状态
  echo "WiFi信息:"
  echo "  设备: $WIFI_DEVICE"
  echo "  服务: $WIFI_SERVICE"
  echo "  状态: $(get_network_status "$WIFI_SERVICE")"

  if [[ "$(get_network_status "$WIFI_SERVICE")" == "Enabled" ]]; then
    local current_wifi=$(networksetup -getairportnetwork "$WIFI_DEVICE" 2>/dev/null | cut -d':' -f2 | xargs)
    local wifi_ip=$(get_network_ip "$WIFI_DEVICE")
    echo "  当前网络: $current_wifi"
    echo "  IP地址: ${wifi_ip:-未获取}"

    # 显示热点状态
    if [[ "$current_wifi" == "$HOTSPOT_NAME" ]]; then
      echo "  热点连接: ✓ 已连接"
    else
      echo "  热点连接: ✗ 未连接到指定热点"
    fi
  fi

  echo ""

  # 显示有线网络状态
  echo "有线网络信息:"
  if networksetup -listallnetworkservices | grep -q "$ETHERNET_SERVICE"; then
    local eth_device=$(get_network_device_info "$ETHERNET_SERVICE")
    local eth_ip=$(get_network_ip "$eth_device")
    echo "  服务: $ETHERNET_SERVICE"
    echo "  设备: $eth_device"
    echo "  状态: $(get_network_status "$ETHERNET_SERVICE")"
    echo "  IP地址: ${eth_ip:-未获取}"
  else
    echo "  状态: 未检测到有线网络服务"
  fi

  echo ""

  # 显示监控状态
  echo "监控状态:"
  check_monitor_status

  echo ""

  # 显示路由表
  echo "当前路由表:"
  netstat -rn | grep -E "default|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\." | head -10

  echo ""

  # 显示热点状态
  echo "热点状态检查:"
  local current_wifi=$(networksetup -getairportnetwork "$WIFI_DEVICE" 2>/dev/null | cut -d':' -f2 | xargs)

  if [[ "$current_wifi" == "$HOTSPOT_NAME" ]]; then
    echo "  $HOTSPOT_NAME: ✅ 已连接"
  elif is_hotspot_visible "$HOTSPOT_NAME"; then
    echo "  $HOTSPOT_NAME: ✓ 可见但未连接"
  else
    echo "  $HOTSPOT_NAME: ❌ 不可见"
  fi

  echo ""

  # 显示护网安全状态
  show_security_status

  echo ""

  # 显示代理配置状态
  show_proxy_status

  echo ""

  # 显示FlClash状态
  show_flclash_status

  echo ""

  # 护网安全检查
  echo "护网安全检查:"
  local current_mode=$(detect_current_network_mode)
  verify_network_security "$current_mode"
}

# 显示护网安全状态
show_security_status() {
  echo "护网安全状态:"

  # 安全模式状态
  if [[ "$SECURITY_MODE" == "true" ]]; then
    echo "  安全模式: ✅ 已启用（护网期间）"
  else
    echo "  安全模式: ❌ 未启用"
  fi

  # FlClash安全检查
  if is_flclash_running; then
    # 检查当前网络模式
    local wifi_enabled=$(get_network_status "$WIFI_SERVICE")
    local eth_enabled=$(get_network_status "$ETHERNET_SERVICE")

    if [[ "$eth_enabled" == "Enabled" && "$wifi_enabled" == "Disabled" ]]; then
      echo "  FlClash状态: ⚠️  运行中（有线模式下存在安全风险）"
      echo "  安全建议: 护网期间建议关闭FlClash"
    elif [[ "$eth_enabled" == "Enabled" && "$wifi_enabled" == "Enabled" ]]; then
      echo "  FlClash状态: ⚠️  运行中（双网络模式，注意流量隔离）"
      echo "  安全建议: 确保办公网流量不经过代理"
    else
      echo "  FlClash状态: ✅ 运行中（仅热点模式，相对安全）"
    fi
  else
    echo "  FlClash状态: ✅ 已关闭（符合护网安全要求）"
  fi

  # 网络模式安全评估
  local current_mode=$(detect_current_network_mode)
  case "$current_mode" in
    "ethernet")
      echo "  网络安全: ✅ 有线直连模式，符合护网要求"
      ;;
    "hotspot")
      echo "  网络安全: ⚠️  热点模式，注意数据安全"
      ;;
    "secure")
      echo "  网络安全: ⚠️  双网络模式，确保办公流量隔离"
      ;;
    *)
      echo "  网络安全: ❓ 未知网络状态"
      ;;
  esac
}

# 检测当前网络模式
detect_current_network_mode() {
  local wifi_enabled=$(get_network_status "$WIFI_SERVICE")
  local eth_enabled=$(get_network_status "$ETHERNET_SERVICE")

  if [[ "$wifi_enabled" == "Enabled" && "$eth_enabled" == "Enabled" ]]; then
    echo "secure"
  elif [[ "$wifi_enabled" == "Enabled" && "$eth_enabled" == "Disabled" ]]; then
    echo "hotspot"
  elif [[ "$wifi_enabled" == "Disabled" && "$eth_enabled" == "Enabled" ]]; then
    echo "ethernet"
  else
    echo "unknown"
  fi
}

# 显示帮助信息
show_help() {
  cat << 'EOF'
网络切换工具
用法: ./network_switch.sh [选项]

基本模式:
  hotspot         - 启用热点模式 (只使用WiFi热点，带重试机制)
  ethernet        - 启用有线网络模式 (只使用有线网络)
  secure          - 启用安全模式 (双网络+流量隔离，代理走热点)

监控模式:
  hotspot-monitor - 启用热点模式并持续监控连接状态
  secure-monitor  - 启用安全模式并持续监控连接状态

管理功能:
  status          - 显示详细的网络状态信息
  stop-monitor    - 停止当前运行的监控进程
  check-monitor   - 检查监控进程运行状态

调试功能:
  scan            - 扫描可用WiFi网络
  test-hotspot    - 测试热点连接（不改变当前网络配置）
  test-network    - 测试当前网络连通性（DNS、Google访问）
  verify-secure   - 验证安全模式配置和流量分流

护网安全功能:
  security-check  - 显示护网安全状态检查
  force-secure    - 强制进入最安全模式（关闭FlClash + 有线直连）
  flclash-status  - 显示FlClash运行状态
  flclash-start   - 手动启动FlClash
  flclash-stop    - 手动关闭FlClash
  flclash-config  - 显示FlClash配置建议
  flclash-proxy   - 测试并启动FlClash代理功能
  vpn-check       - 检查VPN连接路由配置

代理管理功能:
  proxy-status    - 显示当前代理配置状态
  proxy-enable    - 手动启用代理配置
  proxy-disable   - 手动禁用代理配置

环境变量:
  DEBUG_MODE=true - 启用调试模式，显示详细日志

示例:
  # 切换到热点模式
  ./network_switch.sh hotspot

  # 启用安全模式并持续监控
  ./network_switch.sh secure-monitor

  # 在调试模式下查看状态
  DEBUG_MODE=true ./network_switch.sh status

  # 停止监控进程
  ./network_switch.sh stop-monitor

配置文件位置:
  日志文件: ~/network_switch.log
  PID文件: /tmp/network_switch_monitor.pid

注意事项:
  - 监控模式会在后台持续运行
  - 安全模式需要sudo权限来配置路由表
  - 热点连接支持自动激活iPhone热点功能
  - 护网期间：有线模式会自动关闭FlClash确保网络安全
  - 安全模式下办公网流量强制直连，不经过任何代理
EOF
}

# 扫描可用WiFi网络
scan_wifi_networks() {
  log "INFO" "扫描可用WiFi网络"
  echo "可用WiFi网络:"
  /System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport -s
}

# 测试热点连接
test_hotspot_connection() {
  log "INFO" "测试热点连接 (不改变当前配置)"

  # 保存当前WiFi状态
  local original_wifi=$(networksetup -getairportnetwork "$WIFI_DEVICE" 2>/dev/null | cut -d':' -f2 | xargs)
  local wifi_was_on=$(networksetup -getairportpower "$WIFI_DEVICE" | awk '{print $4}')

  log "INFO" "当前WiFi: $original_wifi, WiFi开关: $wifi_was_on"

  # 测试热点连接
  if try_connect_hotspot "$HOTSPOT_NAME" "$HOTSPOT_PASSWORD"; then
    log "INFO" "热点连接测试成功"

    # 如果原来连接的是其他网络，尝试恢复
    if [[ "$original_wifi" != "$HOTSPOT_NAME" && -n "$original_wifi" && "$original_wifi" != "You are not associated with an AirPort network." ]]; then
      log "INFO" "恢复原始WiFi连接: $original_wifi"
      networksetup -setairportnetwork "$WIFI_DEVICE" "$original_wifi"
    fi
  else
    log "ERROR" "热点连接测试失败"
    return 1
  fi
}

# 主函数
main() {
  # 检查依赖
  if ! check_dependencies; then
    exit 1
  fi

  # 处理命令行参数
  case "${1:-help}" in
    hotspot)
      enable_hotspot_mode
      ;;
    ethernet)
      enable_ethernet_mode
      ;;
    secure)
      enable_secure_mode
      ;;
    hotspot-monitor)
      monitor_network_connection "hotspot"
      ;;
    secure-monitor)
      monitor_network_connection "secure"
      ;;
    status)
      show_enhanced_status
      ;;
    stop-monitor)
      stop_monitor
      ;;
    check-monitor)
      check_monitor_status
      ;;
    scan)
      scan_wifi_networks
      ;;
    test-hotspot)
      test_hotspot_connection
      ;;
    test-network)
      log "INFO" "测试当前网络连通性"
      echo "DNS解析测试:"
      if test_network_connectivity "dns"; then
        echo "  ✅ DNS解析正常"
      else
        echo "  ❌ DNS解析失败"
      fi

      echo "Google访问测试:"
      if test_network_connectivity "google"; then
        echo "  ✅ 可以访问Google"
      else
        echo "  ❌ 无法访问Google"
      fi

      echo "FlClash状态:"
      if is_flclash_running; then
        echo "  ✅ FlClash运行中"
      else
        echo "  ❌ FlClash未运行"
      fi
      ;;
    verify-secure)
      log "INFO" "执行安全模式验证"
      verify_secure_mode_complete
      ;;
    security-check)
      show_security_status
      ;;
    force-secure)
      log "INFO" "强制进入最安全模式（护网专用）"
      secure_stop_flclash
      enable_ethernet_mode
      ;;
    flclash-status)
      show_flclash_status
      ;;
    flclash-start)
      start_flclash
      ;;
    flclash-stop)
      secure_stop_flclash
      ;;
    flclash-config)
      show_flclash_config_suggestions
      ;;
    flclash-proxy)
      log "INFO" "测试FlClash API并启动代理功能"
      if is_flclash_running; then
        auto_start_flclash_proxy
      else
        log "ERROR" "FlClash未运行，请先启动FlClash"
      fi
      ;;
    vpn-check)
      diagnose_vpn_connection
      ;;
    proxy-status)
      show_proxy_status
      ;;
    proxy-enable)
      configure_persistent_proxy
      ;;
    proxy-disable)
      clear_persistent_proxy
      ;;
    help|--help|-h)
      show_help
      ;;
    *)
      echo "错误: 未知选项 '$1'"
      echo "使用 '$0 help' 查看帮助信息"
      exit 1
      ;;
  esac
}

# 执行主函数
main "$@"
